# 🛡️ Hydration Safety Guidelines

## Overview
This document provides guidelines and patterns to prevent React hydration errors in Next.js applications.

## ❌ Common Hydration Error Patterns

### 1. Date/Time Usage in Initial State
```typescript
// ❌ WRONG - Causes hydration mismatch
const [date, setDate] = useState(new Date())
const [timestamp, setTimestamp] = useState(Date.now())

// ✅ CORRECT - Use safe date hook
const { date, isClient } = useSafeDate()
```

### 2. Random Values in Render
```typescript
// ❌ WRONG - Different values on server/client
const id = Math.random().toString(36)

// ✅ CORRECT - Generate on client only
const [id, setId] = useState<string>()
useEffect(() => setId(Math.random().toString(36)), [])
```

### 3. Browser API Access
```typescript
// ❌ WRONG - window not available on server
const width = window.innerWidth

// ✅ CORRECT - Check client-side first
const [width, setWidth] = useState(0)
useEffect(() => setWidth(window.innerWidth), [])
```

### 4. Local Storage Access
```typescript
// ❌ WRONG - localStorage not available on server
const [theme, setTheme] = useState(localStorage.getItem('theme'))

// ✅ CORRECT - Use client-only hook
const { value: theme } = useClientOnly(() => localStorage.getItem('theme'))
```

## ✅ Safe Patterns

### 1. Client-Only Hook Pattern
```typescript
export function useClientOnly<T>(clientValue: () => T, serverValue?: T) {
  const [value, setValue] = useState<T | undefined>(serverValue)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    setValue(clientValue())
  }, [])

  return { value, isClient }
}
```

### 2. Safe Date Handling
```typescript
export function useSafeDate(initialDate?: Date) {
  const [date, setDate] = useState<Date | undefined>(initialDate)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    if (!initialDate) {
      setDate(new Date())
    }
  }, [initialDate])

  return { date, isClient, setDate }
}
```

### 3. Conditional Rendering
```typescript
function MyComponent() {
  const [isClient, setIsClient] = useState(false)
  
  useEffect(() => setIsClient(true), [])
  
  return (
    <div>
      {isClient ? (
        <ClientOnlyComponent />
      ) : (
        <ServerSafeComponent />
      )}
    </div>
  )
}
```

## 🔍 ESLint Rules (Recommended)

Add these rules to your `.eslintrc.js` to catch potential hydration issues:

```javascript
module.exports = {
  rules: {
    // Warn about new Date() in useState
    'no-restricted-syntax': [
      'warn',
      {
        selector: 'CallExpression[callee.name="useState"] NewExpression[callee.name="Date"]',
        message: 'Avoid new Date() in useState - use useSafeDate hook instead'
      }
    ],
    
    // Warn about Math.random() in components
    'no-restricted-globals': [
      'warn',
      {
        name: 'Math.random',
        message: 'Math.random() can cause hydration mismatches - use useEffect for client-only random values'
      }
    ]
  }
}
```

## 🧪 Testing for Hydration Issues

### 1. Manual Testing
```bash
# Build and start production server
bun run build
bun run start

# Check browser console for hydration warnings
```

### 2. Automated Testing
```typescript
// __tests__/hydration.test.tsx
import { render } from '@testing-library/react'
import { renderToString } from 'react-dom/server'

test('component renders consistently on server and client', () => {
  const serverHTML = renderToString(<MyComponent />)
  const { container } = render(<MyComponent />)
  
  // Compare server and client HTML
  expect(container.innerHTML).toBe(serverHTML)
})
```

## 📋 Hydration Safety Checklist

- [ ] No `new Date()` in component initial state
- [ ] No `Math.random()` in render functions
- [ ] No browser API access without client checks
- [ ] No localStorage/sessionStorage in initial state
- [ ] Use `useEffect` for client-only operations
- [ ] Implement proper loading states
- [ ] Test with production build
- [ ] Check browser console for warnings

## 🚀 Quick Fixes

### Replace Direct Date Usage
```typescript
// Before
const [date, setDate] = useState(new Date())

// After
const { date, isClient } = useSafeDate()
```

### Replace Browser API Access
```typescript
// Before
const userAgent = navigator.userAgent

// After
const { value: userAgent } = useClientOnly(() => navigator.userAgent)
```

### Replace Random Values
```typescript
// Before
const id = `item-${Math.random()}`

// After
const [id, setId] = useState<string>()
useEffect(() => setId(`item-${Math.random()}`), [])
```

## 📚 Resources

- [Next.js Hydration Error Documentation](https://nextjs.org/docs/messages/react-hydration-error)
- [React SSR Best Practices](https://react.dev/reference/react-dom/server)
- [Hydration-Safe Hooks Library](./lib/hooks/use-safe-date.ts)
