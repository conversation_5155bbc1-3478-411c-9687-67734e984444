import { NextRequest, NextResponse } from 'next/server'

// Cache for 5 minutes to reduce server load
const CACHE_DURATION = 5 * 60 * 1000
let cachedData: any = null
let cacheTimestamp = 0

// Optimized data generation (moved from client-side)
function generatePerformanceData() {
  const now = Date.now()
  
  // Return cached data if still valid
  if (cachedData && (now - cacheTimestamp) < CACHE_DURATION) {
    return cachedData
  }

  // Generate overview metrics
  const overview = {
    totalReach: 2400000,
    engagementRate: 4.2,
    clickThroughRate: 2.1,
    followerGrowth: 12500,
    shareRate: 1.8,
    performanceScore: 87,
    trends: {
      reach: 18.2,
      engagement: 0.8,
      clicks: -0.3,
      followers: 24.1,
      shares: 0.5,
      score: 5
    }
  }

  // Generate trends data (optimized)
  const trendsData = Array.from({ length: 10 }, (_, i) => ({
    date: new Date(Date.now() - (9 - i) * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    reach: Math.floor(Math.random() * 40000) + 45000,
    engagement: Math.round((Math.random() * 2.5 + 3.0) * 10) / 10,
    clicks: Math.floor(Math.random() * 1000) + 800,
    followers: Math.floor(Math.random() * 500) + 12000 + (i * 100)
  }))

  // Generate platform performance
  const platforms = [
    { name: "Instagram", reach: 890000, engagement: 5.2, growth: 12.3, score: 92 },
    { name: "LinkedIn", reach: 456000, engagement: 4.8, growth: 8.7, score: 88 },
    { name: "Facebook", reach: 678000, engagement: 3.9, growth: -2.1, score: 75 },
    { name: "Twitter", reach: 234000, engagement: 3.2, growth: 5.4, score: 72 },
    { name: "TikTok", reach: 567000, engagement: 6.8, growth: 28.9, score: 85 }
  ]

  // Generate goals data
  const goals = [
    {
      title: "Monthly Reach Target",
      current: 2400000,
      target: 3000000,
      progress: 80,
      status: "on-track",
      deadline: "End of Month"
    },
    {
      title: "Engagement Rate Goal", 
      current: 4.2,
      target: 5.0,
      progress: 84,
      status: "on-track",
      deadline: "Quarterly"
    },
    {
      title: "Follower Growth",
      current: 12500,
      target: 15000,
      progress: 83,
      status: "on-track", 
      deadline: "Monthly"
    }
  ]

  const data = {
    overview,
    trends: trendsData,
    platforms,
    goals,
    timestamp: now
  }

  // Cache the data
  cachedData = data
  cacheTimestamp = now

  return data
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const section = searchParams.get('section')

    const data = generatePerformanceData()

    // Return specific section if requested
    if (section && data[section as keyof typeof data]) {
      return NextResponse.json(
        { success: true, data: data[section as keyof typeof data] },
        { 
          headers: {
            'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600',
            'Content-Type': 'application/json'
          }
        }
      )
    }

    // Return all data
    return NextResponse.json(
      { success: true, data },
      { 
        headers: {
          'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600',
          'Content-Type': 'application/json'
        }
      }
    )
  } catch (error) {
    console.error('Performance API Error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch performance data' },
      { status: 500 }
    )
  }
}
