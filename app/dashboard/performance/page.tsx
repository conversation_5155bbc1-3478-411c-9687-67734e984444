import type { <PERSON>ada<PERSON> } from "next"
import { Suspense, lazy } from "react"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { PerformanceHeader } from "@/components/dashboard/performance/performance-header"
import { PerformanceOverview } from "@/components/dashboard/performance/performance-overview"
import { OptimizedChartSkeleton } from "@/lib/chart-provider-optimized"

// Lazy load ultra-optimized components for better performance
const UltraOptimizedPerformanceTrends = lazy(() => import("@/components/dashboard/performance/ultra-optimized-performance-trends").then(module => ({ default: module.UltraOptimizedPerformanceTrends })))
const VirtualizedTopPerformingContent = lazy(() => import("@/components/dashboard/performance/VirtualizedTopPerformingContent").then(module => ({ default: module.VirtualizedTopPerformingContent })))
const PlatformPerformance = lazy(() => import("@/components/dashboard/performance/platform-performance").then(module => ({ default: module.PlatformPerformance })))
const PerformanceGoals = lazy(() => import("@/components/dashboard/performance/performance-goals").then(module => ({ default: module.PerformanceGoals })))
const PerformanceInsights = lazy(() => import("@/components/dashboard/performance/performance-insights").then(module => ({ default: module.PerformanceInsights })))
const PerformanceMonitor = lazy(() => import("@/components/dashboard/performance/performance-monitor").then(module => ({ default: module.PerformanceMonitor })))

export const metadata: Metadata = {
  title: "Performance | Social Media Dashboard",
  description: "Detailed performance analytics and insights",
}

export default function PerformancePage() {
  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        <PerformanceHeader />
        <PerformanceOverview />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <Suspense fallback={<OptimizedChartSkeleton height={400} />}>
            <UltraOptimizedPerformanceTrends />
          </Suspense>
          <Suspense fallback={<OptimizedChartSkeleton height={400} />}>
            <PerformanceGoals />
          </Suspense>
        </div>
        <Suspense fallback={<OptimizedChartSkeleton height={500} />}>
          <VirtualizedTopPerformingContent />
        </Suspense>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <Suspense fallback={<OptimizedChartSkeleton height={400} />}>
            <PlatformPerformance />
          </Suspense>
          <Suspense fallback={<OptimizedChartSkeleton height={400} />}>
            <PerformanceInsights />
          </Suspense>
        </div>
      </div>
      <Suspense fallback={null}>
        <PerformanceMonitor />
      </Suspense>
    </DashboardLayout>
  )
}
