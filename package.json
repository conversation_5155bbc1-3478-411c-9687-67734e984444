{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "ENABLE_PERFORMANCE_METRICS=false ENABLE_PERFORMANCE_LOGGING=false bun --bun run next dev --turbo", "dev:webpack": "bun --bun run next dev", "dev:debug": "ENABLE_PERFORMANCE_METRICS=true ENABLE_PERFORMANCE_LOGGING=true NODE_ENV=development bun --bun run next dev --turbo", "dev:clean": "node scripts/cache-manager.js clean && ENABLE_PERFORMANCE_METRICS=false bun --bun run next dev --turbo", "dev:minimal": "NODE_ENV=development DISABLE_ANIMATIONS=true LAZY_LOAD_CHARTS=true bun run dev:fast", "build": "bun --bun run next build", "build:analyze": "ANALYZE=true bun --bun run next build", "start": "bun --bun run next start", "lint": "bun --bun run next lint", "test": "bun test", "test:run": "bun test", "test:ui": "bun test --ui", "test:coverage": "bun test --coverage", "test:watch": "bun test --watch", "test:debug": "bun test --reporter=verbose", "debug:bundle": "node scripts/analyze-bundle.js", "debug:performance": "node scripts/dashboard-performance-analyzer.js", "optimize:dev": "node scripts/optimize-dev.js", "analyze:performance": "node scripts/performance-optimizer.js analyze", "dev:turbo-fast": "NODE_OPTIONS='--max-old-space-size=8192' ENABLE_PERFORMANCE_METRICS=false bun --bun run next dev --turbo", "fix:types": "bun run tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "cache:clean": "node scripts/cache-manager.js clean", "cache:analyze": "node scripts/cache-manager.js analyze", "cache:optimize": "node scripts/cache-manager.js optimize", "clean": "node scripts/cache-manager.js clean --force && rm -rf .turbo .bun-cache", "fresh": "npm run clean && bun install", "dev:production-like": "NODE_ENV=production bun --bun run next dev --turbo", "analyze:dashboard": "node scripts/dashboard-performance-analyzer.js", "optimize:dashboard": "node scripts/dashboard-performance-analyzer.js && echo 'Check the report above for optimization suggestions'", "optimize:performance": "node scripts/optimize-dashboard.js", "dev:fast": "ENABLE_PERFORMANCE_METRICS=false ENABLE_PERFORMANCE_LOGGING=false bun --bun run next dev --turbo", "analyze:bundle": "ANALYZE=true bun run build", "test:firefox": "node scripts/test-firefox-performance.js", "test:firefox-optimizations": "node scripts/test-firefox-optimizations.js", "optimize:images": "next-optimized-images", "cache:warm": "bun run build && bun run start --dry-run", "bundle:analyze": "ANALYZE=true bun run build", "bundle:size": "find .next/static/chunks -name \"*.js\" -exec du -b {} \\; | awk '{sum += $1} END {print \"Total JS: \" sum/1024/1024 \" MB\"}'", "bundle:monitor": "node scripts/analyze-dependencies.js && npm run bundle:size", "optimize:quick": "node scripts/quick-optimize.js", "perf:verify": "node scripts/verify-performance-optimizations.js", "perf:analyze": "node scripts/advanced-bundle-analyzer.js", "perf:test": "node scripts/performance-testing.js", "perf:monitor": "npm run perf:verify && npm run perf:analyze", "perf:full": "npm run build && npm run perf:monitor && npm run perf:test"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-table": "^8.11.8", "@types/react-window": "^1.8.8", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "critters": "^0.0.25", "date-fns": "^3.3.1", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "14.2.18", "next-themes": "^0.2.1", "qrcode.react": "^3.1.0", "react": "^18.3.1", "react-day-picker": "^8.10.0", "react-dom": "^18.3.1", "react-hook-form": "^7.50.1", "react-resizable-panels": "^2.1.7", "react-window": "^1.8.11", "recharts": "^2.12.0", "sonner": "^1.4.3", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.15.30", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.5.1", "jsdom": "^26.1.0", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5", "vitest": "^3.2.2", "webpack-bundle-analyzer": "^4.10.2"}}