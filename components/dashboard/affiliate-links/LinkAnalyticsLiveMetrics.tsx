// ... LinkAnalyticsLiveMetrics.tsx
import React from "react"
import { LiveMetricsCards } from "./live-metrics-cards"

interface LinkAnalyticsLiveMetricsProps {
  metrics: any
  isLive: boolean
  previousMetrics: any
}

export const LinkAnalyticsLiveMetrics = React.memo(function LinkAnalyticsLiveMetrics({
  metrics,
  isLive,
  previousMetrics,
}: LinkAnalyticsLiveMetricsProps) {
  return <LiveMetricsCards metrics={metrics} isLive={isLive} previousMetrics={previousMetrics} />
})
