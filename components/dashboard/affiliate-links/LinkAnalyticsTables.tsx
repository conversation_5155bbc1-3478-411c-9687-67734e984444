// ... LinkAnalyticsTables.tsx
import React from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface LinkAnalyticsTablesProps {
  referrerData: any[]
  geographicData: any[]
}

export const LinkAnalyticsTables = React.memo(function LinkAnalyticsTables({
  referrerData,
  geographicData,
}: LinkAnalyticsTablesProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h2 className="font-semibold mb-2">Referrer Breakdown</h2>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Source</TableHead>
              <TableHead>Clicks</TableHead>
              <TableHead>Conversions</TableHead>
              <TableHead>Conversion Rate</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {referrerData.map((row, i) => (
              <TableRow key={i}>
                <TableCell>{row.source}</TableCell>
                <TableCell>{row.clicks}</TableCell>
                <TableCell>{row.conversions}</TableCell>
                <TableCell>{row.conversionRate}%</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      <div>
        <h2 className="font-semibold mb-2">Geographic Breakdown</h2>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Country</TableHead>
              <TableHead>Clicks</TableHead>
              <TableHead>Percentage</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {geographicData.map((row, i) => (
              <TableRow key={i}>
                <TableCell>{row.country}</TableCell>
                <TableCell>{row.clicks}</TableCell>
                <TableCell>{row.percentage}%</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
})
