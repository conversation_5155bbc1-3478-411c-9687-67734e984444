// ... LinkAnalyticsConnectionAlert.tsx
import React from "react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Zap } from "lucide-react"

interface LinkAnalyticsConnectionAlertProps {
  status: string
  showConnectionInfo: boolean
}

export const LinkAnalyticsConnectionAlert = React.memo(function LinkAnalyticsConnectionAlert({
  status,
  showConnectionInfo,
}: LinkAnalyticsConnectionAlertProps) {
  if (!showConnectionInfo) return null
  return (
    <Alert
      variant={status === "connected" ? "default" : status === "error" ? "destructive" : "outline"}
      className="transition-all duration-500"
    >
      <Zap className="h-4 w-4" />
      <AlertTitle>
        {status === "connected"
          ? "WebSocket Connected"
          : status === "error"
            ? "Connection Error"
            : "Connection Status"}
      </AlertTitle>
      <AlertDescription>
        {status === "connected"
          ? "You're receiving real-time analytics data from the server."
          : status === "error"
            ? "Unable to connect to the real-time data stream. Using fallback data."
            : "Connection status: " + status}
      </AlertDescription>
    </Alert>
  )
})
