// ... LinkAnalyticsCharts.tsx
import React from "react"
import { Area, AreaChart, Bar, BarChart, Cell, Pie, PieChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"

interface LinkAnalyticsChartsProps {
  clicksOverTime: any[]
  deviceData: any[]
  geographicData: any[]
  referrerData: any[]
}

export const LinkAnalyticsCharts = React.memo(function LinkAnalyticsCharts({
  clicksOverTime,
  deviceData,
  geographicData,
  referrerData,
}: LinkAnalyticsChartsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Clicks Over Time</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={200}>
            <AreaChart data={clicksOverTime}>
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip content={<ChartTooltip />} />
              <Area type="monotone" dataKey="clicks" stroke="#8884d8" fill="#8884d8" />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      {/* Add more charts for deviceData, geographicData, referrerData as needed */}
    </div>
  )
})

// You can further split each chart into its own memoized component if needed.
