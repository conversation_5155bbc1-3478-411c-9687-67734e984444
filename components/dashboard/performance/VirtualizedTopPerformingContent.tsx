"use client"

import { useState, memo, useMemo, use<PERSON><PERSON>back, useEffect } from "react"
import { FixedSizeList as List } from "react-window"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Eye, Heart, MessageSquare, Share2, ExternalLink } from "lucide-react"

interface ContentItem {
  id: number
  title: string
  type: string
  platform: string
  date: string
  reach: number
  engagement: number
  likes: number
  comments: number
  shares: number
  score: number
}

// Generate larger dataset for virtual scrolling demonstration
const generateContentData = (count: number): ContentItem[] => {
  const types = ["Video", "Story", "Post", "Article", "Carousel", "Reel", "Live"]
  const platforms = ["Instagram", "LinkedIn", "Twitter", "Facebook", "TikTok", "YouTube"]
  const titles = [
    "5 Social Media Trends for 2024",
    "Behind the Scenes: Product Development", 
    "Customer Success Spotlight",
    "Industry Report: Q1 Insights",
    "Quick Tips Tuesday",
    "Weekly Roundup",
    "Product Launch Announcement",
    "Team Spotlight",
    "Educational Content Series",
    "User Generated Content Feature"
  ]

  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    title: titles[i % titles.length] + ` #${Math.floor(i / titles.length) + 1}`,
    type: types[Math.floor(Math.random() * types.length)],
    platform: platforms[Math.floor(Math.random() * platforms.length)],
    date: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    reach: Math.floor(Math.random() * 200000) + 10000,
    engagement: Math.round((Math.random() * 10 + 2) * 10) / 10,
    likes: Math.floor(Math.random() * 15000) + 500,
    comments: Math.floor(Math.random() * 1000) + 50,
    shares: Math.floor(Math.random() * 2000) + 100,
    score: Math.floor(Math.random() * 40) + 60,
  }))
}

const ITEM_HEIGHT = 70
const TABLE_HEIGHT = 500

interface RowProps {
  index: number
  style: React.CSSProperties
  data: {
    items: ContentItem[]
    getScoreColor: (score: number) => string
  }
}

const ContentRow = memo(function ContentRow({ index, style, data }: RowProps) {
  const { items, getScoreColor } = data
  const content = items[index]

  if (!content) return null

  return (
    <div style={style} className="flex items-center border-b px-4 py-2">
      {/* Content */}
      <div className="flex w-[300px] flex-shrink-0 flex-col px-2">
        <div className="font-medium truncate">{content.title}</div>
        <div className="text-sm text-muted-foreground">
          <Badge variant="outline" className="mr-1 text-xs">
            {content.type}
          </Badge>
        </div>
      </div>

      {/* Platform */}
      <div className="w-[100px] flex-shrink-0 px-2 text-sm">{content.platform}</div>

      {/* Date */}
      <div className="w-[100px] flex-shrink-0 px-2 text-sm">{content.date}</div>

      {/* Reach */}
      <div className="w-[100px] flex-shrink-0 px-2 text-right text-sm">
        {content.reach.toLocaleString()}
      </div>

      {/* Engagement */}
      <div className="w-[100px] flex-shrink-0 px-2 text-right text-sm">
        {content.engagement}%
      </div>

      {/* Likes */}
      <div className="w-[100px] flex-shrink-0 px-2 text-right text-sm">
        {content.likes.toLocaleString()}
      </div>

      {/* Comments */}
      <div className="w-[100px] flex-shrink-0 px-2 text-right text-sm">
        {content.comments.toLocaleString()}
      </div>

      {/* Shares */}
      <div className="w-[100px] flex-shrink-0 px-2 text-right text-sm">
        {content.shares.toLocaleString()}
      </div>

      {/* Score */}
      <div className="w-[80px] flex-shrink-0 px-2 text-right">
        <Badge className={`${getScoreColor(content.score)} text-white text-xs`}>
          {content.score}
        </Badge>
      </div>

      {/* Actions */}
      <div className="w-[60px] flex-shrink-0 px-2 text-right">
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <ExternalLink className="h-3 w-3" />
        </Button>
      </div>
    </div>
  )
})

export const VirtualizedTopPerformingContent = memo(function VirtualizedTopPerformingContent() {
  const [sortBy, setSortBy] = useState("score")
  const [timeframe, setTimeframe] = useState("30days")
  const [contentData, setContentData] = useState<ContentItem[] | null>(null)

  useEffect(() => {
    setContentData(generateContentData(500))
  }, [])

  if (!contentData) {
    return <div>Loading...</div>
  }

  const sortedContent = useMemo(() => {
    return [...contentData].sort((a, b) => {
      if (sortBy === "score") return b.score - a.score
      if (sortBy === "engagement") return b.engagement - a.engagement
      if (sortBy === "reach") return b.reach - a.reach
      return 0
    })
  }, [contentData, sortBy])

  const getScoreColor = useCallback((score: number) => {
    if (score >= 90) return "bg-emerald-500"
    if (score >= 80) return "bg-blue-500"
    if (score >= 70) return "bg-orange-500"
    return "bg-red-500"
  }, [])

  const itemData = useMemo(
    () => ({
      items: sortedContent,
      getScoreColor,
    }),
    [sortedContent, getScoreColor]
  )

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
          <div>
            <CardTitle>Top Performing Content</CardTitle>
            <CardDescription>
              Your best content ranked by performance score ({sortedContent.length.toLocaleString()} items)
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={timeframe} onValueChange={setTimeframe}>
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7days">Last 7 Days</SelectItem>
                <SelectItem value="30days">Last 30 Days</SelectItem>
                <SelectItem value="90days">Last 90 Days</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="score">Performance Score</SelectItem>
                <SelectItem value="engagement">Engagement Rate</SelectItem>
                <SelectItem value="reach">Reach</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          {/* Table Header */}
          <div className="flex items-center border-b bg-muted/50 px-4 py-3 text-sm font-medium">
            <div className="w-[300px] flex-shrink-0 px-2">Content</div>
            <div className="w-[100px] flex-shrink-0 px-2">Platform</div>
            <div className="w-[100px] flex-shrink-0 px-2">Date</div>
            <div className="w-[100px] flex-shrink-0 px-2 text-right">
              <div className="flex items-center justify-end">
                <Eye className="mr-1 h-4 w-4" />
                Reach
              </div>
            </div>
            <div className="w-[100px] flex-shrink-0 px-2 text-right">Engagement</div>
            <div className="w-[100px] flex-shrink-0 px-2 text-right">
              <div className="flex items-center justify-end">
                <Heart className="mr-1 h-4 w-4" />
                Likes
              </div>
            </div>
            <div className="w-[100px] flex-shrink-0 px-2 text-right">
              <div className="flex items-center justify-end">
                <MessageSquare className="mr-1 h-4 w-4" />
                Comments
              </div>
            </div>
            <div className="w-[100px] flex-shrink-0 px-2 text-right">
              <div className="flex items-center justify-end">
                <Share2 className="mr-1 h-4 w-4" />
                Shares
              </div>
            </div>
            <div className="w-[80px] flex-shrink-0 px-2 text-right">Score</div>
            <div className="w-[60px] flex-shrink-0 px-2"></div>
          </div>

          {/* Virtualized Table Body */}
          <List
            height={TABLE_HEIGHT}
            width="100%"
            itemCount={sortedContent.length}
            itemSize={ITEM_HEIGHT}
            itemData={itemData}
            overscanCount={10}
          >
            {ContentRow}
          </List>
        </div>
      </CardContent>
    </Card>
  )
})
