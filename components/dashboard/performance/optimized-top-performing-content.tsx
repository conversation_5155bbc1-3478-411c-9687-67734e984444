"use client"

import { useState, memo, useMemo, useCallback } from "react"
import { FixedSizeList as List } from "react-window"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Eye, Heart, MessageCircle, Share2, ExternalLink, Search } from "lucide-react"
import { useContentData } from "@/lib/hooks/use-performance-data"

interface ContentItem {
  id: number
  title: string
  type: string
  platform: string
  date: string
  reach: number
  engagement: number
  likes: number
  comments: number
  shares: number
  score: number
}

interface RowProps {
  index: number
  style: React.CSSProperties
  data: {
    items: ContentItem[]
    getScoreColor: (score: number) => string
  }
}

// Virtualized row component for performance
const ContentRow = memo(function ContentRow({ index, style, data }: RowProps) {
  const content = data.items[index]
  
  if (!content) {
    return (
      <div style={style} className="flex items-center justify-center p-4">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    )
  }

  return (
    <div style={style} className="flex items-center p-4 border-b border-border hover:bg-muted/50">
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-3">
          <div className="flex-1 min-w-0">
            <h4 className="font-medium truncate">{content.title}</h4>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>{content.type}</span>
              <span>•</span>
              <span>{content.platform}</span>
              <span>•</span>
              <span>{content.date}</span>
            </div>
          </div>
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              <span>{content.reach.toLocaleString()}</span>
            </div>
            <div className="flex items-center gap-1">
              <span>{content.engagement}%</span>
            </div>
            <div className="flex items-center gap-1">
              <Heart className="h-4 w-4" />
              <span>{content.likes.toLocaleString()}</span>
            </div>
            <div className="flex items-center gap-1">
              <MessageCircle className="h-4 w-4" />
              <span>{content.comments.toLocaleString()}</span>
            </div>
            <div className="flex items-center gap-1">
              <Share2 className="h-4 w-4" />
              <span>{content.shares.toLocaleString()}</span>
            </div>
            <Badge className={`${data.getScoreColor(content.score)} text-white`}>
              {content.score}
            </Badge>
            <Button variant="ghost" size="sm">
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
})

export const OptimizedTopPerformingContent = memo(function OptimizedTopPerformingContent() {
  const [sortBy, setSortBy] = useState("score")
  const [timeframe, setTimeframe] = useState("30days")
  const [search, setSearch] = useState("")
  const [page, setPage] = useState(1)

  const { data, loading, error } = useContentData({
    page,
    limit: 100, // Load more items per page for virtual scrolling
    sortBy,
    search
  })

  const getScoreColor = useMemo(() => (score: number) => {
    if (score >= 90) return "bg-emerald-500"
    if (score >= 80) return "bg-blue-500"
    if (score >= 70) return "bg-orange-500"
    return "bg-red-500"
  }, [])

  const handleSearchChange = useCallback((value: string) => {
    setSearch(value)
    setPage(1) // Reset to first page when searching
  }, [])

  const handleSortChange = useCallback((value: string) => {
    setSortBy(value)
    setPage(1) // Reset to first page when sorting
  }, [])

  const loadMore = useCallback(() => {
    if (data?.pagination.hasNext) {
      setPage(prev => prev + 1)
    }
  }, [data?.pagination.hasNext])

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Top Performing Content</CardTitle>
          <CardDescription>Error loading content data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[500px] flex items-center justify-center text-muted-foreground">
            Failed to load content data. Please try again.
          </div>
        </CardContent>
      </Card>
    )
  }

  const items = data?.data || []

  return (
    <Card>
      <CardHeader>
        <CardTitle>Top Performing Content</CardTitle>
        <CardDescription>
          Your best performing content across all platforms
        </CardDescription>
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search content..."
              value={search}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Select value={sortBy} onValueChange={handleSortChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="score">Performance Score</SelectItem>
              <SelectItem value="engagement">Engagement Rate</SelectItem>
              <SelectItem value="reach">Reach</SelectItem>
              <SelectItem value="date">Date</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        {loading && items.length === 0 ? (
          <div className="h-[500px] flex items-center justify-center">
            <div className="text-muted-foreground">Loading content...</div>
          </div>
        ) : (
          <div className="h-[500px] border rounded-lg">
            <List
              height={500}
              width="100%"
              itemCount={items.length}
              itemSize={80}
              itemData={{
                items,
                getScoreColor
              }}
              overscanCount={5}
            >
              {ContentRow}
            </List>
          </div>
        )}
        
        {data?.pagination && (
          <div className="mt-4 flex items-center justify-between text-sm text-muted-foreground">
            <div>
              Showing {items.length} of {data.pagination.total} items
            </div>
            {data.pagination.hasNext && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={loadMore}
                disabled={loading}
              >
                Load More
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
})
