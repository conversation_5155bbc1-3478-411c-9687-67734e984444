"use client"

import { useState, memo, Suspense } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { OptimizedChart, OptimizedChartSkeleton, convertToOptimizedFormat } from "@/lib/chart-provider-optimized"
import { usePerformanceData } from "@/lib/hooks/use-performance-data"

// Optimized chart components using Canvas instead of Recharts
const ReachAreaChart = memo(function ReachAreaChart({ data }: { data: any[] }) {
  const chartData = convertToOptimizedFormat(data)
  
  return (
    <OptimizedChart
      type="area"
      data={{
        labels: chartData.labels,
        datasets: [{
          label: "Reach",
          data: data.map(item => item.reach),
          borderColor: "#8884d8",
          backgroundColor: "rgba(136, 132, 216, 0.3)",
          fill: true
        }]
      }}
      height={300}
      className="w-full"
    />
  )
})

const EngagementLineChart = memo(function EngagementLineChart({ data }: { data: any[] }) {
  return (
    <OptimizedChart
      type="line"
      data={{
        labels: data.map(item => item.date),
        datasets: [{
          label: "Engagement Rate",
          data: data.map(item => item.engagement),
          borderColor: "#82ca9d",
          backgroundColor: "rgba(130, 202, 157, 0.1)",
          fill: false
        }]
      }}
      height={300}
      className="w-full"
    />
  )
})

const ClicksAreaChart = memo(function ClicksAreaChart({ data }: { data: any[] }) {
  return (
    <OptimizedChart
      type="area"
      data={{
        labels: data.map(item => item.date),
        datasets: [{
          label: "Clicks",
          data: data.map(item => item.clicks),
          borderColor: "#ffc658",
          backgroundColor: "rgba(255, 198, 88, 0.3)",
          fill: true
        }]
      }}
      height={300}
      className="w-full"
    />
  )
})

const FollowersLineChart = memo(function FollowersLineChart({ data }: { data: any[] }) {
  return (
    <OptimizedChart
      type="line"
      data={{
        labels: data.map(item => item.date),
        datasets: [{
          label: "Followers",
          data: data.map(item => item.followers),
          borderColor: "#ff8042",
          backgroundColor: "rgba(255, 128, 66, 0.1)",
          fill: false
        }]
      }}
      height={300}
      className="w-full"
    />
  )
})

export const OptimizedPerformanceTrends = memo(function OptimizedPerformanceTrends() {
  const [timeframe, setTimeframe] = useState("30days")
  const { data, loading, error } = usePerformanceData({ section: 'trends' })

  // Fallback data for loading state
  const fallbackData = [
    { date: "Jan 1", reach: 45000, engagement: 3.2, clicks: 890, followers: 12450 },
    { date: "Jan 8", reach: 52000, engagement: 3.8, clicks: 1120, followers: 12680 },
    { date: "Jan 15", reach: 48000, engagement: 4.1, clicks: 980, followers: 12890 },
    { date: "Jan 22", reach: 61000, engagement: 4.5, clicks: 1340, followers: 13120 },
    { date: "Jan 29", reach: 58000, engagement: 4.2, clicks: 1250, followers: 13380 },
    { date: "Feb 5", reach: 67000, engagement: 4.8, clicks: 1580, followers: 13650 },
    { date: "Feb 12", reach: 72000, engagement: 5.1, clicks: 1720, followers: 13920 },
    { date: "Feb 19", reach: 69000, engagement: 4.9, clicks: 1650, followers: 14200 },
    { date: "Feb 26", reach: 78000, engagement: 5.3, clicks: 1890, followers: 14520 },
    { date: "Mar 5", reach: 82000, engagement: 5.6, clicks: 2010, followers: 14850 },
  ]

  const trendsData = Array.isArray(data) ? data : fallbackData

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
          <CardDescription>Error loading trends data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center text-muted-foreground">
            Failed to load chart data. Please try again.
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Performance Trends</CardTitle>
        <CardDescription>Track your performance metrics over time</CardDescription>
        <div className="flex items-center gap-2">
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="reach">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="reach">Reach</TabsTrigger>
            <TabsTrigger value="engagement">Engagement</TabsTrigger>
            <TabsTrigger value="clicks">Clicks</TabsTrigger>
            <TabsTrigger value="followers">Followers</TabsTrigger>
          </TabsList>
          <TabsContent value="reach">
            <Suspense fallback={<OptimizedChartSkeleton height={300} />}>
              <ReachAreaChart data={trendsData} />
            </Suspense>
          </TabsContent>
          <TabsContent value="engagement">
            <Suspense fallback={<OptimizedChartSkeleton height={300} />}>
              <EngagementLineChart data={trendsData} />
            </Suspense>
          </TabsContent>
          <TabsContent value="clicks">
            <Suspense fallback={<OptimizedChartSkeleton height={300} />}>
              <ClicksAreaChart data={trendsData} />
            </Suspense>
          </TabsContent>
          <TabsContent value="followers">
            <Suspense fallback={<OptimizedChartSkeleton height={300} />}>
              <FollowersLineChart data={trendsData} />
            </Suspense>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
})
