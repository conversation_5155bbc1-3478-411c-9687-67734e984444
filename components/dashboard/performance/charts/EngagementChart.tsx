"use client"

import { memo } from "react"
import { Optimized<PERSON>hart } from "@/lib/chart-provider-optimized"

interface ChartData {
  date: string
  reach: number
  engagement: number
  clicks: number
  followers: number
}

interface EngagementChartProps {
  data: ChartData[]
  height?: number
  className?: string
}

export const EngagementChart = memo(function EngagementChart({ 
  data, 
  height = 300, 
  className = "w-full" 
}: EngagementChartProps) {
  return (
    <OptimizedChart
      type="line"
      data={{
        labels: data.map(item => item.date),
        datasets: [{
          label: "Engagement Rate",
          data: data.map(item => item.engagement),
          borderColor: "#82ca9d",
          backgroundColor: "rgba(130, 202, 157, 0.1)",
          fill: false
        }]
      }}
      height={height}
      className={className}
    />
  )
})

EngagementChart.displayName = "EngagementChart"
