"use client"

import { memo } from "react"
import { Optimized<PERSON><PERSON> } from "@/lib/chart-provider-optimized"

interface ChartData {
  date: string
  reach: number
  engagement: number
  clicks: number
  followers: number
}

interface FollowersChartProps {
  data: ChartData[]
  height?: number
  className?: string
}

export const FollowersChart = memo(function FollowersChart({ 
  data, 
  height = 300, 
  className = "w-full" 
}: FollowersChartProps) {
  return (
    <OptimizedChart
      type="line"
      data={{
        labels: data.map(item => item.date),
        datasets: [{
          label: "Followers",
          data: data.map(item => item.followers),
          borderColor: "#ff8042",
          backgroundColor: "rgba(255, 128, 66, 0.1)",
          fill: false
        }]
      }}
      height={height}
      className={className}
    />
  )
})

FollowersChart.displayName = "FollowersChart"
