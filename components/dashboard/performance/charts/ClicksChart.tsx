"use client"

import { memo } from "react"
import { Optimized<PERSON>hart } from "@/lib/chart-provider-optimized"

interface ChartData {
  date: string
  reach: number
  engagement: number
  clicks: number
  followers: number
}

interface ClicksChartProps {
  data: ChartData[]
  height?: number
  className?: string
}

export const Clicks<PERSON>hart = memo(function ClicksChart({ 
  data, 
  height = 300, 
  className = "w-full" 
}: ClicksChartProps) {
  return (
    <OptimizedChart
      type="area"
      data={{
        labels: data.map(item => item.date),
        datasets: [{
          label: "Clicks",
          data: data.map(item => item.clicks),
          borderColor: "#ffc658",
          backgroundColor: "rgba(255, 198, 88, 0.3)",
          fill: true
        }]
      }}
      height={height}
      className={className}
    />
  )
})

ClicksChart.displayName = "ClicksChart"
