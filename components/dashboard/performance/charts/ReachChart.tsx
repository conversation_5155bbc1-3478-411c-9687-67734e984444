"use client"

import { memo } from "react"
import { Optimized<PERSON>hart, convertToOptimizedFormat } from "@/lib/chart-provider-optimized"

interface ChartData {
  date: string
  reach: number
  engagement: number
  clicks: number
  followers: number
}

interface ReachChartProps {
  data: ChartData[]
  height?: number
  className?: string
}

export const ReachChart = memo(function ReachChart({ 
  data, 
  height = 300, 
  className = "w-full" 
}: ReachChartProps) {
  const chartData = convertToOptimizedFormat(data)
  
  return (
    <OptimizedChart
      type="area"
      data={{
        labels: chartData.labels,
        datasets: [{
          label: "Reach",
          data: data.map(item => item.reach),
          borderColor: "#8884d8",
          backgroundColor: "rgba(136, 132, 216, 0.3)",
          fill: true
        }]
      }}
      height={height}
      className={className}
    />
  )
})

ReachChart.displayName = "ReachChart"
