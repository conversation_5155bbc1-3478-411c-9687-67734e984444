// ... ContentLibraryFilters.tsx
import React from "react"
import { TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"

interface ContentLibraryFiltersProps {
  filterStatus: string
  setFilterStatus: (v: string) => void
  filterType: string
  setFilterType: (v: string) => void
  filterPlatform: string
  setFilterPlatform: (v: string) => void
  viewMode: "grid" | "list"
  setViewMode: (v: "grid" | "list") => void
}

export const ContentLibraryFilters = React.memo(function ContentLibraryFilters({
  filterStatus,
  setFilterStatus,
  filterType,
  setFilterType,
  filterPlatform,
  setFilterPlatform,
  viewMode,
  setViewMode,
}: ContentLibraryFiltersProps) {
  return (
    <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
      <TabsList>
        <TabsTrigger value="all" onClick={() => setFilterStatus("all")}>All</TabsTrigger>
        <TabsTrigger value="published" onClick={() => setFilterStatus("Published")}>Published</TabsTrigger>
        <TabsTrigger value="scheduled" onClick={() => setFilterStatus("Scheduled")}>Scheduled</TabsTrigger>
        <TabsTrigger value="draft" onClick={() => setFilterStatus("Draft")}>Drafts</TabsTrigger>
      </TabsList>
      <div className="flex flex-wrap gap-2">
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Content Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="Image">Image</SelectItem>
            <SelectItem value="Video">Video</SelectItem>
            <SelectItem value="Article">Article</SelectItem>
            <SelectItem value="Link">Link</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterPlatform} onValueChange={setFilterPlatform}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Platform" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Platforms</SelectItem>
            <SelectItem value="Facebook">Facebook</SelectItem>
            <SelectItem value="Instagram">Instagram</SelectItem>
            <SelectItem value="Twitter">Twitter</SelectItem>
            <SelectItem value="LinkedIn">LinkedIn</SelectItem>
          </SelectContent>
        </Select>
        <div className="flex rounded-md border">
          <Button
            variant={viewMode === "grid" ? "default" : "ghost"}
            size="sm"
            className="rounded-r-none"
            onClick={() => setViewMode("grid")}
          >
            Grid
          </Button>
          <Button
            variant={viewMode === "list" ? "default" : "ghost"}
            size="sm"
            className="rounded-l-none"
            onClick={() => setViewMode("list")}
          >
            List
          </Button>
        </div>
      </div>
    </div>
  )
})
