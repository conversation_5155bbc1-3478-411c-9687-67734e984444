// ... ContentLibraryGrid.tsx
import React from "react"
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Eye, CalendarIcon, Edit } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface ContentLibraryGridProps {
  filteredContent: any[]
  handleViewDetails: (content: any) => void
}

export const ContentLibraryGrid = React.memo(function ContentLibraryGrid({
  filteredContent,
  handleViewDetails,
}: ContentLibraryGridProps) {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
      {filteredContent.map((content) => (
        <Card key={content.id} className="overflow-hidden">
          <div className="relative aspect-video w-full overflow-hidden bg-muted">
            <img
              src={content.image || "/placeholder.svg"}
              alt={content.title}
              className="h-full w-full object-cover transition-all hover:scale-105"
            />
            <div className="absolute right-2 top-2">
              <Badge
                variant={
                  content.status === "Published"
                    ? "default"
                    : content.status === "Scheduled"
                      ? "secondary"
                      : "outline"
                }
              >
                {content.status}
              </Badge>
            </div>
          </div>
          <CardHeader className="p-3">
            <div className="flex items-center justify-between">
              <Badge variant="outline">{content.platform}</Badge>
              <Badge variant="outline">{content.type}</Badge>
            </div>
            <CardTitle className="line-clamp-1 text-base">{content.title}</CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-between p-3 pt-0">
            <div className="flex items-center text-sm text-muted-foreground">
              {content.status === "Published" ? (
                <>
                  <Eye className="mr-1 h-4 w-4" />
                  {content.views.toLocaleString()}
                </>
              ) : content.status === "Scheduled" ? (
                <>
                  <CalendarIcon className="mr-1 h-4 w-4" />
                  {new Date(content.publishDate).toLocaleDateString()}
                </>
              ) : (
                <>
                  <Edit className="mr-1 h-4 w-4" />
                  Draft
                </>
              )}
            </div>
            <Button variant="ghost" size="sm" onClick={() => handleViewDetails(content)}>
              View
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  )
})
