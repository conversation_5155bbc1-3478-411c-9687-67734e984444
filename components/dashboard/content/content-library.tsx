"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Calendar,
  Edit,
  Eye,
  MoreHorizontal,
  Search,
  Trash2,
  CalendarIcon,
  ThumbsUp,
  MessageSquare,
  Share2,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ContentDetails } from "./content-details"
import { ContentLibraryFilters } from "./ContentLibraryFilters"
import { ContentLibraryGrid } from "./ContentLibraryGrid"
import { ContentLibraryList } from "./ContentLibraryList"

const contentData = [
  {
    id: 1,
    title: "10 Tips for Better Social Media Marketing",
    type: "Article",
    platform: "LinkedIn",
    status: "Published",
    publishDate: "2023-07-15",
    likes: 245,
    comments: 32,
    shares: 78,
    views: 1250,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 2,
    title: "New Product Launch Announcement",
    type: "Image",
    platform: "Facebook",
    status: "Published",
    publishDate: "2023-07-10",
    likes: 189,
    comments: 24,
    shares: 45,
    views: 980,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 3,
    title: "Behind the Scenes: Our Team at Work",
    type: "Video",
    platform: "Instagram",
    status: "Published",
    publishDate: "2023-07-05",
    likes: 320,
    comments: 56,
    shares: 89,
    views: 1500,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 4,
    title: "Customer Success Story: ABC Corp",
    type: "Link",
    platform: "Twitter",
    status: "Published",
    publishDate: "2023-07-01",
    likes: 156,
    comments: 18,
    shares: 34,
    views: 750,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 5,
    title: "Industry Trends for 2023",
    type: "Article",
    platform: "LinkedIn",
    status: "Published",
    publishDate: "2023-06-28",
    likes: 210,
    comments: 28,
    shares: 65,
    views: 1100,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 6,
    title: "Holiday Special Promotion",
    type: "Image",
    platform: "Instagram",
    status: "Scheduled",
    publishDate: "2023-12-01",
    likes: 0,
    comments: 0,
    shares: 0,
    views: 0,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 7,
    title: "End of Year Sale Announcement",
    type: "Video",
    platform: "Facebook",
    status: "Scheduled",
    publishDate: "2023-11-25",
    likes: 0,
    comments: 0,
    shares: 0,
    views: 0,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 8,
    title: "Product Feature Highlight",
    type: "Image",
    platform: "Twitter",
    status: "Draft",
    publishDate: "",
    likes: 0,
    comments: 0,
    shares: 0,
    views: 0,
    image: "/placeholder.svg?height=100&width=100",
  },
]

export function ContentLibrary({ onSelectContent }: { onSelectContent?: (content: any) => void }) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedContent, setSelectedContent] = useState<any>(null)
  const [viewDetailsOpen, setViewDetailsOpen] = useState(false)
  const [filterStatus, setFilterStatus] = useState("all")
  const [filterType, setFilterType] = useState("all")
  const [filterPlatform, setFilterPlatform] = useState("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  // Filter content based on search query and filters
  const filteredContent = contentData.filter((content) => {
    const matchesSearch = content.title.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = filterStatus === "all" || content.status === filterStatus
    const matchesType = filterType === "all" || content.type === filterType
    const matchesPlatform = filterPlatform === "all" || content.platform === filterPlatform
    return matchesSearch && matchesStatus && matchesType && matchesPlatform
  })

  const handleViewDetails = (content: any) => {
    setSelectedContent(content)
    setViewDetailsOpen(true)
    if (onSelectContent) {
      onSelectContent(content)
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
          <div>
            <CardTitle>Content Library</CardTitle>
            <CardDescription>Manage and analyze your social media content</CardDescription>
          </div>
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search content..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" className="space-y-4">
          <ContentLibraryFilters
            filterStatus={filterStatus}
            setFilterStatus={setFilterStatus}
            filterType={filterType}
            setFilterType={setFilterType}
            filterPlatform={filterPlatform}
            setFilterPlatform={setFilterPlatform}
            viewMode={viewMode}
            setViewMode={setViewMode}
          />
          <TabsContent value="all" className="space-y-4">
            {viewMode === "grid" ? (
              <ContentLibraryGrid filteredContent={filteredContent} handleViewDetails={handleViewDetails} />
            ) : (
              <ContentLibraryList filteredContent={filteredContent} handleViewDetails={handleViewDetails} />
            )}
          </TabsContent>
        </Tabs>
      </CardContent>

      <Dialog open={viewDetailsOpen} onOpenChange={setViewDetailsOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Content Details</DialogTitle>
            <DialogDescription>Detailed information about the content</DialogDescription>
          </DialogHeader>
          {selectedContent && <ContentDetails content={selectedContent} />}
          <DialogFooter>
            <Button variant="outline" onClick={() => setViewDetailsOpen(false)}>
              Close
            </Button>
            <Button>Edit Content</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
