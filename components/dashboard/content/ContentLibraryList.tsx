// ContentLibraryList.tsx
import React from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Eye, Edit, Calendar, Trash2, MoreHorizontal, ThumbsUp, MessageSquare, Share2 } from "lucide-react"
import { FixedSizeList as List } from "react-window"

interface ContentLibraryListProps {
  filteredContent: any[]
  handleViewDetails: (content: any) => void
}

export const ContentLibraryList = React.memo(function ContentLibraryList({
  filteredContent,
  handleViewDetails,
}: ContentLibraryListProps) {
  // Row renderer for react-window
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => {
    const content = filteredContent[index]
    return (
      <tr key={content.id} className="border-b" style={style}>
        <td className="p-2">
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={content.image || "/placeholder.svg"} alt={content.title} />
              <AvatarFallback>{content.title.substring(0, 2)}</AvatarFallback>
            </Avatar>
            <span className="line-clamp-1 font-medium">{content.title}</span>
          </div>
        </td>
        <td className="p-2">{content.type}</td>
        <td className="p-2">
          <Badge variant="outline">{content.platform}</Badge>
        </td>
        <td className="p-2">
          <Badge
            variant={
              content.status === "Published"
                ? "default"
                : content.status === "Scheduled"
                  ? "secondary"
                  : "outline"
            }
          >
            {content.status}
          </Badge>
        </td>
        <td className="p-2">
          {content.publishDate ? new Date(content.publishDate).toLocaleDateString() : "-"}
        </td>
        <td className="p-2">
          {content.status === "Published" ? (
            <div className="flex items-center gap-2">
              <div className="flex items-center">
                <ThumbsUp className="mr-1 h-3 w-3" />
                {content.likes}
              </div>
              <div className="flex items-center">
                <MessageSquare className="mr-1 h-3 w-3" />
                {content.comments}
              </div>
              <div className="flex items-center">
                <Share2 className="mr-1 h-3 w-3" />
                {content.shares}
              </div>
            </div>
          ) : (
            "-"
          )}
        </td>
        <td className="p-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleViewDetails(content)}>
                <Eye className="mr-2 h-4 w-4" />
                View details
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              {content.status === "Scheduled" && (
                <DropdownMenuItem>
                  <Calendar className="mr-2 h-4 w-4" />
                  Reschedule
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600">
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </td>
      </tr>
    )
  }

  return (
    <div className="rounded-md border">
      <table className="w-full">
        <thead>
          <tr className="border-b bg-muted/50">
            <th className="p-2 text-left font-medium">Title</th>
            <th className="p-2 text-left font-medium">Type</th>
            <th className="p-2 text-left font-medium">Platform</th>
            <th className="p-2 text-left font-medium">Status</th>
            <th className="p-2 text-left font-medium">Date</th>
            <th className="p-2 text-left font-medium">Engagement</th>
            <th className="p-2 text-left font-medium">Actions</th>
          </tr>
        </thead>
        <tbody>
          <List
            height={400}
            itemCount={filteredContent.length}
            itemSize={60}
            width={"100%"}
          >
            {({ index, style }) => Row({ index, style })}
          </List>
        </tbody>
      </table>
    </div>
  )
})
