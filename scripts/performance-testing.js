#!/usr/bin/env node

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

console.log('🧪 Performance Testing Suite Starting...\n');

// Test configurations
const testConfigs = {
  devices: [
    {
      name: 'Desktop',
      viewport: { width: 1920, height: 1080 },
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    },
    {
      name: 'Tablet',
      viewport: { width: 768, height: 1024 },
      userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
    },
    {
      name: 'Mobile',
      viewport: { width: 375, height: 667 },
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
    }
  ],
  networks: [
    {
      name: 'Fast 3G',
      downloadThroughput: 1.5 * 1024 * 1024 / 8, // 1.5 Mbps
      uploadThroughput: 750 * 1024 / 8,           // 750 Kbps
      latency: 150
    },
    {
      name: 'Slow 3G',
      downloadThroughput: 500 * 1024 / 8,         // 500 Kbps
      uploadThroughput: 500 * 1024 / 8,           // 500 Kbps
      latency: 300
    },
    {
      name: 'WiFi',
      downloadThroughput: 10 * 1024 * 1024 / 8,   // 10 Mbps
      uploadThroughput: 5 * 1024 * 1024 / 8,      // 5 Mbps
      latency: 20
    }
  ]
};

// Performance metrics to collect
const performanceMetrics = [
  'firstContentfulPaint',
  'largestContentfulPaint',
  'firstInputDelay',
  'cumulativeLayoutShift',
  'totalBlockingTime',
  'speedIndex'
];

// Test results storage
const testResults = {
  timestamp: new Date().toISOString(),
  tests: [],
  summary: {
    averageLoadTime: 0,
    averageFCP: 0,
    averageLCP: 0,
    passedTests: 0,
    failedTests: 0
  }
};

// Utility functions
function formatTime(ms) {
  return `${ms.toFixed(0)}ms`;
}

function getPerformanceGrade(metric, value) {
  const thresholds = {
    firstContentfulPaint: { good: 1800, poor: 3000 },
    largestContentfulPaint: { good: 2500, poor: 4000 },
    firstInputDelay: { good: 100, poor: 300 },
    cumulativeLayoutShift: { good: 0.1, poor: 0.25 },
    totalBlockingTime: { good: 200, poor: 600 },
    speedIndex: { good: 3400, poor: 5800 }
  };

  const threshold = thresholds[metric];
  if (!threshold) return 'unknown';

  if (value <= threshold.good) return 'good';
  if (value <= threshold.poor) return 'needs-improvement';
  return 'poor';
}

// Performance testing function
async function runPerformanceTest(url, device, network) {
  console.log(`🔍 Testing: ${device.name} on ${network.name}...`);

  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const page = await browser.newPage();
    
    // Set device viewport and user agent
    await page.setViewport(device.viewport);
    await page.setUserAgent(device.userAgent);
    
    // Set network conditions
    const client = await page.target().createCDPSession();
    await client.send('Network.emulateNetworkConditions', {
      offline: false,
      downloadThroughput: network.downloadThroughput,
      uploadThroughput: network.uploadThroughput,
      latency: network.latency
    });

    // Enable performance monitoring
    await page.coverage.startJSCoverage();
    await page.coverage.startCSSCoverage();

    // Start performance measurement
    const startTime = Date.now();
    
    // Navigate to page
    await page.goto(url, { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });

    const loadTime = Date.now() - startTime;

    // Collect performance metrics
    const metrics = await page.evaluate(() => {
      return new Promise((resolve) => {
        // Wait for performance entries to be available
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0];
          const paint = performance.getEntriesByType('paint');
          
          const fcp = paint.find(entry => entry.name === 'first-contentful-paint');
          const lcp = performance.getEntriesByType('largest-contentful-paint')[0];
          
          resolve({
            navigationStart: navigation.navigationStart,
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
            loadComplete: navigation.loadEventEnd - navigation.navigationStart,
            firstContentfulPaint: fcp ? fcp.startTime : 0,
            largestContentfulPaint: lcp ? lcp.startTime : 0,
            domInteractive: navigation.domInteractive - navigation.navigationStart,
            domComplete: navigation.domComplete - navigation.navigationStart
          });
        }, 1000);
      });
    });

    // Collect resource information
    const resourceMetrics = await page.evaluate(() => {
      const resources = performance.getEntriesByType('resource');
      let totalSize = 0;
      let jsSize = 0;
      let cssSize = 0;
      let imageSize = 0;

      resources.forEach(resource => {
        const size = resource.transferSize || 0;
        totalSize += size;

        if (resource.name.includes('.js')) {
          jsSize += size;
        } else if (resource.name.includes('.css')) {
          cssSize += size;
        } else if (resource.name.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) {
          imageSize += size;
        }
      });

      return {
        totalResources: resources.length,
        totalSize,
        jsSize,
        cssSize,
        imageSize
      };
    });

    // Get coverage information
    const jsCoverage = await page.coverage.stopJSCoverage();
    const cssCoverage = await page.coverage.stopCSSCoverage();

    let totalBytes = 0;
    let usedBytes = 0;

    [...jsCoverage, ...cssCoverage].forEach(entry => {
      totalBytes += entry.text.length;
      entry.ranges.forEach(range => {
        usedBytes += range.end - range.start - 1;
      });
    });

    const unusedBytes = totalBytes - usedBytes;
    const codeUtilization = totalBytes > 0 ? (usedBytes / totalBytes) * 100 : 0;

    // Test specific dashboard functionality
    const dashboardMetrics = await page.evaluate(() => {
      // Check if performance dashboard elements are present
      const performanceElements = {
        header: !!document.querySelector('[data-testid="performance-header"]') || 
                !!document.querySelector('h1, h2, h3'),
        charts: document.querySelectorAll('canvas, svg').length,
        tables: document.querySelectorAll('table, [role="table"]').length,
        cards: document.querySelectorAll('[class*="card"]').length
      };

      return performanceElements;
    });

    // Calculate performance score
    const performanceScore = calculatePerformanceScore(metrics, resourceMetrics, codeUtilization);

    const testResult = {
      device: device.name,
      network: network.name,
      timestamp: new Date().toISOString(),
      loadTime,
      metrics,
      resourceMetrics,
      codeUtilization: Math.round(codeUtilization),
      unusedBytes,
      dashboardMetrics,
      performanceScore,
      passed: performanceScore >= 70
    };

    testResults.tests.push(testResult);

    // Log results
    console.log(`  ⏱️  Load Time: ${formatTime(loadTime)}`);
    console.log(`  🎨 FCP: ${formatTime(metrics.firstContentfulPaint)}`);
    console.log(`  🖼️  LCP: ${formatTime(metrics.largestContentfulPaint)}`);
    console.log(`  📊 Performance Score: ${performanceScore}/100`);
    console.log(`  📦 Total Resources: ${resourceMetrics.totalResources}`);
    console.log(`  💾 Total Size: ${(resourceMetrics.totalSize / 1024).toFixed(0)}KB`);
    console.log(`  ♻️  Code Utilization: ${Math.round(codeUtilization)}%`);
    console.log(`  ${testResult.passed ? '✅' : '❌'} Test ${testResult.passed ? 'PASSED' : 'FAILED'}\n`);

    return testResult;

  } catch (error) {
    console.log(`  ❌ Test failed: ${error.message}\n`);
    return {
      device: device.name,
      network: network.name,
      error: error.message,
      passed: false
    };
  } finally {
    await browser.close();
  }
}

function calculatePerformanceScore(metrics, resourceMetrics, codeUtilization) {
  let score = 100;

  // FCP scoring (25 points)
  if (metrics.firstContentfulPaint > 3000) score -= 25;
  else if (metrics.firstContentfulPaint > 1800) score -= 15;
  else if (metrics.firstContentfulPaint > 1000) score -= 5;

  // LCP scoring (25 points)
  if (metrics.largestContentfulPaint > 4000) score -= 25;
  else if (metrics.largestContentfulPaint > 2500) score -= 15;
  else if (metrics.largestContentfulPaint > 1500) score -= 5;

  // Resource size scoring (25 points)
  const totalSizeMB = resourceMetrics.totalSize / (1024 * 1024);
  if (totalSizeMB > 5) score -= 25;
  else if (totalSizeMB > 3) score -= 15;
  else if (totalSizeMB > 2) score -= 5;

  // Code utilization scoring (25 points)
  if (codeUtilization < 50) score -= 25;
  else if (codeUtilization < 70) score -= 15;
  else if (codeUtilization < 85) score -= 5;

  return Math.max(0, Math.round(score));
}

// Generate summary report
function generateSummary() {
  const passedTests = testResults.tests.filter(test => test.passed).length;
  const totalTests = testResults.tests.length;
  
  const avgLoadTime = testResults.tests.reduce((sum, test) => 
    sum + (test.loadTime || 0), 0) / totalTests;
  
  const avgFCP = testResults.tests.reduce((sum, test) => 
    sum + (test.metrics?.firstContentfulPaint || 0), 0) / totalTests;
  
  const avgLCP = testResults.tests.reduce((sum, test) => 
    sum + (test.metrics?.largestContentfulPaint || 0), 0) / totalTests;

  const avgScore = testResults.tests.reduce((sum, test) => 
    sum + (test.performanceScore || 0), 0) / totalTests;

  testResults.summary = {
    totalTests,
    passedTests,
    failedTests: totalTests - passedTests,
    passRate: Math.round((passedTests / totalTests) * 100),
    averageLoadTime: Math.round(avgLoadTime),
    averageFCP: Math.round(avgFCP),
    averageLCP: Math.round(avgLCP),
    averageScore: Math.round(avgScore)
  };

  console.log('📊 Performance Testing Summary');
  console.log('================================');
  console.log(`Tests Run: ${totalTests}`);
  console.log(`Passed: ${passedTests} (${testResults.summary.passRate}%)`);
  console.log(`Failed: ${testResults.summary.failedTests}`);
  console.log(`Average Load Time: ${formatTime(avgLoadTime)}`);
  console.log(`Average FCP: ${formatTime(avgFCP)}`);
  console.log(`Average LCP: ${formatTime(avgLCP)}`);
  console.log(`Average Score: ${avgScore}/100`);

  if (testResults.summary.passRate >= 80) {
    console.log('\n🎉 Excellent performance across all test conditions!');
  } else if (testResults.summary.passRate >= 60) {
    console.log('\n👍 Good performance, but some optimization opportunities exist.');
  } else {
    console.log('\n⚠️ Performance issues detected. Optimization recommended.');
  }
}

// Save detailed report
function saveReport() {
  const reportPath = path.join(process.cwd(), 'performance-test-results.json');
  fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
  console.log(`\n📄 Detailed results saved to: ${reportPath}`);
}

// Main execution
async function main() {
  const url = process.argv[2] || 'http://localhost:3000/dashboard/performance';
  
  console.log(`🎯 Testing URL: ${url}\n`);

  try {
    // Run tests for each device and network combination
    for (const device of testConfigs.devices) {
      for (const network of testConfigs.networks) {
        await runPerformanceTest(url, device, network);
      }
    }

    generateSummary();
    saveReport();

    console.log('\n✨ Performance Testing Complete!');

  } catch (error) {
    console.error('❌ Error during testing:', error.message);
    process.exit(1);
  }
}

// Check if puppeteer is available
try {
  require.resolve('puppeteer');
  main();
} catch (error) {
  console.log('❌ Puppeteer not found. Install with: npm install --save-dev puppeteer');
  console.log('⚠️ Skipping performance testing...');
}
