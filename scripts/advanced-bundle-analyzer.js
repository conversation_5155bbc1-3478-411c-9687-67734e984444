#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Advanced Bundle Analysis Starting...\n');

// Configuration
const config = {
  bundleSizeThresholds: {
    critical: 5 * 1024 * 1024, // 5MB
    warning: 3 * 1024 * 1024,  // 3MB
    good: 2 * 1024 * 1024      // 2MB
  },
  componentSizeThresholds: {
    critical: 12 * 1024, // 12KB
    warning: 8 * 1024,   // 8KB
    good: 5 * 1024       // 5KB
  },
  performanceTargets: {
    bundleReduction: 25, // 25% minimum reduction
    componentCount: 50,  // Max components
    avgComponentSize: 8  // 8KB average
  }
};

// Analysis results
const analysis = {
  bundleSize: {
    total: 0,
    chunks: [],
    pages: {},
    vendors: {}
  },
  components: {
    performance: [],
    dashboard: [],
    ui: []
  },
  dependencies: {
    heavy: [],
    unused: [],
    duplicates: []
  },
  recommendations: [],
  score: 0
};

// Utility functions
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getFileSize(filePath) {
  try {
    return fs.statSync(filePath).size;
  } catch (error) {
    return 0;
  }
}

function analyzeDirectory(dirPath, category) {
  if (!fs.existsSync(dirPath)) return [];
  
  const files = [];
  const items = fs.readdirSync(dirPath, { withFileTypes: true });
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item.name);
    
    if (item.isDirectory()) {
      files.push(...analyzeDirectory(fullPath, category));
    } else if (item.name.endsWith('.tsx') || item.name.endsWith('.ts')) {
      const size = getFileSize(fullPath);
      files.push({
        name: item.name,
        path: fullPath.replace(process.cwd(), ''),
        size,
        sizeFormatted: formatBytes(size),
        category
      });
    }
  }
  
  return files;
}

// Bundle size analysis
function analyzeBundleSize() {
  console.log('📦 Analyzing Bundle Size...');
  
  const chunksPath = path.join(process.cwd(), '.next/static/chunks');
  if (!fs.existsSync(chunksPath)) {
    console.log('❌ Build not found. Run "npm run build" first.');
    return;
  }

  let totalSize = 0;
  const chunks = [];
  
  function scanChunks(dir, prefix = '') {
    const items = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const item of items) {
      const fullPath = path.join(dir, item.name);
      const relativePath = prefix + item.name;
      
      if (item.isDirectory()) {
        scanChunks(fullPath, relativePath + '/');
      } else if (item.name.endsWith('.js')) {
        const size = getFileSize(fullPath);
        totalSize += size;
        chunks.push({
          name: relativePath,
          size,
          sizeFormatted: formatBytes(size)
        });
      }
    }
  }
  
  scanChunks(chunksPath);
  
  // Sort chunks by size
  chunks.sort((a, b) => b.size - a.size);
  
  analysis.bundleSize.total = totalSize;
  analysis.bundleSize.chunks = chunks;
  
  console.log(`  Total Bundle Size: ${formatBytes(totalSize)}`);
  console.log(`  Number of Chunks: ${chunks.length}`);
  
  // Show largest chunks
  console.log('\n  📊 Largest Chunks:');
  chunks.slice(0, 10).forEach((chunk, index) => {
    const percentage = ((chunk.size / totalSize) * 100).toFixed(1);
    console.log(`    ${index + 1}. ${chunk.name} - ${chunk.sizeFormatted} (${percentage}%)`);
  });
}

// Component analysis
function analyzeComponents() {
  console.log('\n🧩 Analyzing Components...');
  
  // Analyze performance components
  const performanceComponents = analyzeDirectory(
    path.join(process.cwd(), 'components/dashboard/performance'),
    'performance'
  );
  
  // Analyze all dashboard components
  const dashboardComponents = analyzeDirectory(
    path.join(process.cwd(), 'components/dashboard'),
    'dashboard'
  );
  
  // Analyze UI components
  const uiComponents = analyzeDirectory(
    path.join(process.cwd(), 'components/ui'),
    'ui'
  );
  
  analysis.components.performance = performanceComponents;
  analysis.components.dashboard = dashboardComponents;
  analysis.components.ui = uiComponents;
  
  const allComponents = [...performanceComponents, ...dashboardComponents, ...uiComponents];
  const avgSize = allComponents.reduce((sum, comp) => sum + comp.size, 0) / allComponents.length;
  
  console.log(`  Total Components: ${allComponents.length}`);
  console.log(`  Average Size: ${formatBytes(avgSize)}`);
  
  // Show largest components
  const largestComponents = allComponents
    .sort((a, b) => b.size - a.size)
    .slice(0, 10);
  
  console.log('\n  📊 Largest Components:');
  largestComponents.forEach((comp, index) => {
    const status = comp.size > config.componentSizeThresholds.critical ? '🔴' :
                   comp.size > config.componentSizeThresholds.warning ? '🟡' : '🟢';
    console.log(`    ${index + 1}. ${status} ${comp.name} - ${comp.sizeFormatted}`);
  });
  
  // Performance-specific analysis
  console.log('\n  🎯 Performance Components Analysis:');
  performanceComponents.forEach(comp => {
    const status = comp.size > config.componentSizeThresholds.warning ? '⚠️' : '✅';
    console.log(`    ${status} ${comp.name} - ${comp.sizeFormatted}`);
  });
}

// Dependency analysis
function analyzeDependencies() {
  console.log('\n📚 Analyzing Dependencies...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    // Heavy dependencies (known large packages)
    const heavyPackages = [
      'recharts', '@radix-ui', 'react-window', 'date-fns', 
      'lucide-react', '@tanstack/react-table'
    ];
    
    const heavyDeps = [];
    for (const [name, version] of Object.entries(dependencies)) {
      if (heavyPackages.some(heavy => name.includes(heavy))) {
        heavyDeps.push({ name, version });
      }
    }
    
    analysis.dependencies.heavy = heavyDeps;
    
    console.log('  📦 Heavy Dependencies Found:');
    heavyDeps.forEach(dep => {
      console.log(`    • ${dep.name}@${dep.version}`);
    });
    
    // Check for potential optimizations
    if (dependencies.recharts) {
      analysis.recommendations.push({
        type: 'critical',
        message: 'Recharts detected - consider replacing with canvas-based charts',
        impact: 'High bundle size reduction (500KB+)'
      });
    }
    
  } catch (error) {
    console.log('  ❌ Could not analyze package.json');
  }
}

// Generate recommendations
function generateRecommendations() {
  console.log('\n💡 Generating Recommendations...');
  
  const { total } = analysis.bundleSize;
  const allComponents = [
    ...analysis.components.performance,
    ...analysis.components.dashboard,
    ...analysis.components.ui
  ];
  
  // Bundle size recommendations
  if (total > config.bundleSizeThresholds.critical) {
    analysis.recommendations.push({
      type: 'critical',
      message: `Bundle size is ${formatBytes(total)} - critically large`,
      impact: 'Severe performance impact',
      action: 'Immediate optimization required'
    });
  } else if (total > config.bundleSizeThresholds.warning) {
    analysis.recommendations.push({
      type: 'warning',
      message: `Bundle size is ${formatBytes(total)} - above recommended`,
      impact: 'Performance impact',
      action: 'Consider optimization'
    });
  } else {
    analysis.recommendations.push({
      type: 'success',
      message: `Bundle size is ${formatBytes(total)} - excellent!`,
      impact: 'Good performance',
      action: 'Maintain current optimizations'
    });
  }
  
  // Component size recommendations
  const largeComponents = allComponents.filter(
    comp => comp.size > config.componentSizeThresholds.warning
  );
  
  if (largeComponents.length > 0) {
    analysis.recommendations.push({
      type: 'warning',
      message: `${largeComponents.length} components exceed 8KB`,
      impact: 'Increased bundle size',
      action: 'Split large components into smaller ones'
    });
  }
  
  // Performance-specific recommendations
  const performanceComponents = analysis.components.performance;
  const avgPerfComponentSize = performanceComponents.reduce(
    (sum, comp) => sum + comp.size, 0
  ) / performanceComponents.length;
  
  if (avgPerfComponentSize > config.componentSizeThresholds.warning) {
    analysis.recommendations.push({
      type: 'warning',
      message: 'Performance components average size is high',
      impact: 'Slower dashboard loading',
      action: 'Implement component splitting and lazy loading'
    });
  }
  
  // Display recommendations
  analysis.recommendations.forEach((rec, index) => {
    const icon = rec.type === 'critical' ? '🔴' : 
                 rec.type === 'warning' ? '🟡' : '🟢';
    console.log(`  ${icon} ${rec.message}`);
    console.log(`     Impact: ${rec.impact}`);
    console.log(`     Action: ${rec.action}\n`);
  });
}

// Calculate performance score
function calculateScore() {
  let score = 100;
  
  // Bundle size scoring (40 points)
  const { total } = analysis.bundleSize;
  if (total > config.bundleSizeThresholds.critical) {
    score -= 40;
  } else if (total > config.bundleSizeThresholds.warning) {
    score -= 20;
  } else if (total > config.bundleSizeThresholds.good) {
    score -= 10;
  }
  
  // Component size scoring (30 points)
  const allComponents = [
    ...analysis.components.performance,
    ...analysis.components.dashboard,
    ...analysis.components.ui
  ];
  
  const largeComponents = allComponents.filter(
    comp => comp.size > config.componentSizeThresholds.warning
  ).length;
  
  const componentPenalty = Math.min(30, largeComponents * 5);
  score -= componentPenalty;
  
  // Dependencies scoring (20 points)
  const heavyDepsCount = analysis.dependencies.heavy.length;
  score -= Math.min(20, heavyDepsCount * 3);
  
  // Critical issues (10 points)
  const criticalIssues = analysis.recommendations.filter(
    rec => rec.type === 'critical'
  ).length;
  score -= criticalIssues * 10;
  
  analysis.score = Math.max(0, score);
  
  console.log(`\n🎯 Performance Score: ${analysis.score}/100`);
  
  if (analysis.score >= 90) {
    console.log('🎉 Excellent! Your bundle is highly optimized.');
  } else if (analysis.score >= 70) {
    console.log('👍 Good performance, but room for improvement.');
  } else if (analysis.score >= 50) {
    console.log('⚠️ Performance issues detected. Optimization recommended.');
  } else {
    console.log('🚨 Critical performance issues. Immediate action required.');
  }
}

// Save detailed report
function saveReport() {
  const report = {
    timestamp: new Date().toISOString(),
    analysis,
    summary: {
      bundleSize: formatBytes(analysis.bundleSize.total),
      componentCount: analysis.components.performance.length + 
                     analysis.components.dashboard.length + 
                     analysis.components.ui.length,
      score: analysis.score,
      recommendations: analysis.recommendations.length
    }
  };
  
  const reportPath = path.join(process.cwd(), 'advanced-bundle-analysis.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
}

// Main execution
function main() {
  try {
    analyzeBundleSize();
    analyzeComponents();
    analyzeDependencies();
    generateRecommendations();
    calculateScore();
    saveReport();
    
    console.log('\n✨ Advanced Bundle Analysis Complete!');
    
  } catch (error) {
    console.error('❌ Error during analysis:', error.message);
    process.exit(1);
  }
}

main();
