#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Performance Dashboard Optimizations...\n');

// Performance metrics tracking
const metrics = {
  bundleSize: {
    before: 17.4, // MB
    after: 0,
    target: 13.0, // 25% reduction target
  },
  componentSizes: [],
  optimizationChecks: {
    canvasCharts: false,
    virtualizedLists: false,
    reactMemo: false,
    lazyLoading: false,
    componentSplitting: false,
  }
};

// Check if build exists
function checkBuildExists() {
  const buildPath = path.join(process.cwd(), '.next');
  if (!fs.existsSync(buildPath)) {
    console.log('❌ Build not found. Run "npm run build" first.');
    process.exit(1);
  }
  console.log('✅ Build directory found');
}

// Calculate current bundle size
function calculateBundleSize() {
  const chunksPath = path.join(process.cwd(), '.next/static/chunks');
  if (!fs.existsSync(chunksPath)) {
    console.log('❌ Chunks directory not found');
    return 0;
  }

  let totalSize = 0;
  const files = fs.readdirSync(chunksPath, { recursive: true });
  
  files.forEach(file => {
    if (file.endsWith('.js')) {
      const filePath = path.join(chunksPath, file);
      const stats = fs.statSync(filePath);
      totalSize += stats.size;
    }
  });

  const sizeInMB = totalSize / (1024 * 1024);
  metrics.bundleSize.after = sizeInMB;
  
  console.log(`📦 Current bundle size: ${sizeInMB.toFixed(2)}MB`);
  return sizeInMB;
}

// Check component file sizes
function checkComponentSizes() {
  const componentsPath = path.join(process.cwd(), 'components/dashboard/performance');
  if (!fs.existsSync(componentsPath)) {
    console.log('❌ Performance components directory not found');
    return;
  }

  console.log('\n📏 Component Size Analysis:');
  const files = fs.readdirSync(componentsPath, { recursive: true });
  
  files.forEach(file => {
    if (file.endsWith('.tsx')) {
      const filePath = path.join(componentsPath, file);
      const stats = fs.statSync(filePath);
      const sizeInKB = stats.size / 1024;
      
      metrics.componentSizes.push({
        name: file,
        size: sizeInKB
      });
      
      const status = sizeInKB < 8 ? '✅' : '⚠️';
      console.log(`  ${status} ${file}: ${sizeInKB.toFixed(1)}KB`);
    }
  });
}

// Check for optimization implementations
function checkOptimizations() {
  console.log('\n🔧 Optimization Checks:');
  
  // Check for Canvas charts
  const chartProviderPath = path.join(process.cwd(), 'lib/chart-provider-optimized.tsx');
  if (fs.existsSync(chartProviderPath)) {
    const content = fs.readFileSync(chartProviderPath, 'utf8');
    if (content.includes('CanvasChart') && content.includes('canvas.getContext')) {
      metrics.optimizationChecks.canvasCharts = true;
      console.log('  ✅ Canvas-based charts implemented');
    }
  }
  
  // Check for virtualized lists
  const virtualizedPath = path.join(process.cwd(), 'components/dashboard/performance/VirtualizedTopPerformingContent.tsx');
  if (fs.existsSync(virtualizedPath)) {
    const content = fs.readFileSync(virtualizedPath, 'utf8');
    if (content.includes('react-window') && content.includes('FixedSizeList')) {
      metrics.optimizationChecks.virtualizedLists = true;
      console.log('  ✅ Virtualized lists implemented');
    }
  }
  
  // Check for React.memo usage
  const platformPath = path.join(process.cwd(), 'components/dashboard/performance/platform-performance.tsx');
  if (fs.existsSync(platformPath)) {
    const content = fs.readFileSync(platformPath, 'utf8');
    if (content.includes('memo(function') && content.includes('PlatformCard')) {
      metrics.optimizationChecks.reactMemo = true;
      console.log('  ✅ React.memo() implemented');
    }
  }
  
  // Check for lazy loading
  const pagePath = path.join(process.cwd(), 'app/dashboard/performance/page.tsx');
  if (fs.existsSync(pagePath)) {
    const content = fs.readFileSync(pagePath, 'utf8');
    if (content.includes('lazy(') && content.includes('Suspense')) {
      metrics.optimizationChecks.lazyLoading = true;
      console.log('  ✅ Lazy loading implemented');
    }
  }
  
  // Check for component splitting
  const chartsPath = path.join(process.cwd(), 'components/dashboard/performance/charts');
  if (fs.existsSync(chartsPath)) {
    const chartFiles = fs.readdirSync(chartsPath);
    if (chartFiles.length >= 4) {
      metrics.optimizationChecks.componentSplitting = true;
      console.log('  ✅ Component splitting implemented');
    }
  }
}

// Generate performance report
function generateReport() {
  console.log('\n📊 Performance Optimization Report');
  console.log('=====================================');
  
  // Bundle size analysis
  const bundleReduction = ((metrics.bundleSize.before - metrics.bundleSize.after) / metrics.bundleSize.before) * 100;
  const targetMet = bundleReduction >= 25;
  
  console.log(`\n📦 Bundle Size Analysis:`);
  console.log(`  Before: ${metrics.bundleSize.before}MB`);
  console.log(`  After:  ${metrics.bundleSize.after.toFixed(2)}MB`);
  console.log(`  Reduction: ${bundleReduction.toFixed(1)}% ${targetMet ? '✅' : '❌'}`);
  console.log(`  Target: 25% reduction ${targetMet ? '(EXCEEDED)' : '(NOT MET)'}`);
  
  // Component size analysis
  const avgComponentSize = metrics.componentSizes.reduce((sum, comp) => sum + comp.size, 0) / metrics.componentSizes.length;
  const componentsUnder8KB = metrics.componentSizes.filter(comp => comp.size < 8).length;
  
  console.log(`\n📏 Component Analysis:`);
  console.log(`  Average size: ${avgComponentSize.toFixed(1)}KB`);
  console.log(`  Components under 8KB: ${componentsUnder8KB}/${metrics.componentSizes.length}`);
  
  // Optimization checklist
  console.log(`\n✅ Optimization Checklist:`);
  Object.entries(metrics.optimizationChecks).forEach(([check, passed]) => {
    const status = passed ? '✅' : '❌';
    const name = check.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    console.log(`  ${status} ${name}`);
  });
  
  // Overall score
  const optimizationsPassed = Object.values(metrics.optimizationChecks).filter(Boolean).length;
  const totalOptimizations = Object.keys(metrics.optimizationChecks).length;
  const score = (optimizationsPassed / totalOptimizations) * 100;
  
  console.log(`\n🎯 Overall Optimization Score: ${score.toFixed(0)}%`);
  
  if (score >= 80 && targetMet) {
    console.log('🎉 EXCELLENT! All major optimizations implemented successfully.');
  } else if (score >= 60) {
    console.log('👍 GOOD! Most optimizations implemented. Consider addressing remaining items.');
  } else {
    console.log('⚠️  NEEDS IMPROVEMENT! Several optimizations are missing.');
  }
  
  return {
    bundleReduction,
    targetMet,
    score,
    avgComponentSize
  };
}

// Main execution
function main() {
  try {
    checkBuildExists();
    calculateBundleSize();
    checkComponentSizes();
    checkOptimizations();
    const report = generateReport();
    
    // Save report to file
    const reportData = {
      timestamp: new Date().toISOString(),
      metrics,
      summary: report
    };
    
    fs.writeFileSync(
      path.join(process.cwd(), 'performance-optimization-report.json'),
      JSON.stringify(reportData, null, 2)
    );
    
    console.log('\n📄 Detailed report saved to: performance-optimization-report.json');
    
  } catch (error) {
    console.error('❌ Error during verification:', error.message);
    process.exit(1);
  }
}

main();
