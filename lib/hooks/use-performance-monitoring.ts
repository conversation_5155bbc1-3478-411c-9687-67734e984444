"use client"

import { useState, useEffect, useCallback, useRef } from 'react'

interface PerformanceMetrics {
  loadTime: number
  renderTime: number
  memoryUsage: number
  chartRenderTimes: Record<string, number>
  scrollPerformance: number
  isFirefox: boolean
  bundleSize?: number
  componentCount: number
  errorCount: number
}

interface PerformanceAlert {
  type: 'warning' | 'error' | 'info'
  message: string
  metric: string
  value: number
  threshold: number
  timestamp: number
}

export function usePerformanceMonitoring() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    chartRenderTimes: {},
    scrollPerformance: 0,
    isFirefox: false,
    componentCount: 0,
    errorCount: 0
  })

  const [alerts, setAlerts] = useState<PerformanceAlert[]>([])
  const observerRef = useRef<PerformanceObserver | null>(null)
  const startTimeRef = useRef<number>(0)

  // Performance thresholds
  const thresholds = {
    loadTime: 3000, // 3 seconds
    renderTime: 1000, // 1 second
    memoryUsage: 100, // 100MB
    chartRenderTime: 500, // 500ms
    scrollPerformance: 16.67 // 60fps = 16.67ms per frame
  }

  const addAlert = useCallback((alert: Omit<PerformanceAlert, 'timestamp'>) => {
    // Only add alerts on the client side
    if (typeof window === 'undefined') return

    const newAlert: PerformanceAlert = {
      ...alert,
      timestamp: Date.now()
    }
    setAlerts(prev => [...prev.slice(-9), newAlert]) // Keep last 10 alerts
  }, [])

  const measureChartRender = useCallback((chartName: string) => {
    // Only measure on the client side
    if (typeof window === 'undefined' || typeof performance === 'undefined') {
      return () => {} // Return no-op function for SSR
    }

    const startTime = performance.now()

    return () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime

      setMetrics(prev => ({
        ...prev,
        chartRenderTimes: {
          ...prev.chartRenderTimes,
          [chartName]: renderTime
        }
      }))

      if (renderTime > thresholds.chartRenderTime) {
        addAlert({
          type: 'warning',
          message: `Chart ${chartName} took ${renderTime.toFixed(0)}ms to render`,
          metric: 'chartRenderTime',
          value: renderTime,
          threshold: thresholds.chartRenderTime
        })
      }
    }
  }, [addAlert])

  const measureScrollPerformance = useCallback(() => {
    // Only measure on the client side
    if (typeof window === 'undefined' || typeof performance === 'undefined' || typeof requestAnimationFrame === 'undefined') {
      return
    }

    let frameCount = 0
    let totalTime = 0
    let lastTime = performance.now()

    const measureFrame = () => {
      const currentTime = performance.now()
      const frameTime = currentTime - lastTime
      totalTime += frameTime
      frameCount++
      lastTime = currentTime

      if (frameCount >= 60) { // Measure over 60 frames
        const avgFrameTime = totalTime / frameCount
        setMetrics(prev => ({
          ...prev,
          scrollPerformance: avgFrameTime
        }))

        if (avgFrameTime > thresholds.scrollPerformance) {
          addAlert({
            type: 'warning',
            message: `Scroll performance degraded: ${avgFrameTime.toFixed(1)}ms per frame`,
            metric: 'scrollPerformance',
            value: avgFrameTime,
            threshold: thresholds.scrollPerformance
          })
        }

        frameCount = 0
        totalTime = 0
      }

      requestAnimationFrame(measureFrame)
    }

    requestAnimationFrame(measureFrame)
  }, [addAlert])

  const measureMemoryUsage = useCallback(() => {
    // Only measure on the client side
    if (typeof window === 'undefined' || typeof performance === 'undefined') {
      return
    }

    if ('memory' in performance) {
      const memory = (performance as any).memory
      const usedMB = memory.usedJSHeapSize / 1024 / 1024

      setMetrics(prev => ({
        ...prev,
        memoryUsage: usedMB
      }))

      if (usedMB > thresholds.memoryUsage) {
        addAlert({
          type: 'error',
          message: `High memory usage: ${usedMB.toFixed(1)}MB`,
          metric: 'memoryUsage',
          value: usedMB,
          threshold: thresholds.memoryUsage
        })
      }
    }
  }, [addAlert])

  const measureLoadTime = useCallback(() => {
    // Only measure on the client side
    if (typeof window === 'undefined' || typeof performance === 'undefined') {
      return
    }

    const loadTime = performance.now() - startTimeRef.current
    setMetrics(prev => ({
      ...prev,
      loadTime
    }))

    if (loadTime > thresholds.loadTime) {
      addAlert({
        type: 'warning',
        message: `Slow page load: ${loadTime.toFixed(0)}ms`,
        metric: 'loadTime',
        value: loadTime,
        threshold: thresholds.loadTime
      })
    }
  }, [addAlert])

  // Initialize performance monitoring
  useEffect(() => {
    // Only run on the client side
    if (typeof window === 'undefined' || typeof navigator === 'undefined' || typeof performance === 'undefined') {
      return
    }

    // Initialize start time on client
    startTimeRef.current = performance.now()

    const isFirefox = navigator.userAgent.includes('Firefox')
    setMetrics(prev => ({ ...prev, isFirefox }))

    // Set up Performance Observer
    if ('PerformanceObserver' in window) {
      observerRef.current = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'measure') {
            console.log(`Performance: ${entry.name} took ${entry.duration}ms`)
          }

          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming
            setMetrics(prev => ({
              ...prev,
              loadTime: navEntry.loadEventEnd - navEntry.fetchStart
            }))
          }
        })
      })

      try {
        observerRef.current.observe({
          entryTypes: ['measure', 'navigation', 'paint']
        })
      } catch (error) {
        console.warn('Performance Observer not fully supported:', error)
      }
    }

    // Initial measurements
    measureLoadTime()
    measureMemoryUsage()
    measureScrollPerformance()

    // Set up periodic monitoring
    const memoryInterval = setInterval(measureMemoryUsage, 30000) // Every 30 seconds
    const loadTimeTimeout = setTimeout(measureLoadTime, 1000) // After 1 second

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
      clearInterval(memoryInterval)
      clearTimeout(loadTimeTimeout)
    }
  }, [measureLoadTime, measureMemoryUsage, measureScrollPerformance])

  // Component counting
  useEffect(() => {
    // Only run on the client side
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return
    }

    const componentElements = document.querySelectorAll('[data-component]')
    setMetrics(prev => ({
      ...prev,
      componentCount: componentElements.length
    }))
  }, [])

  // Error tracking
  useEffect(() => {
    // Only run on the client side
    if (typeof window === 'undefined') {
      return
    }

    const handleError = (event: ErrorEvent) => {
      setMetrics(prev => ({
        ...prev,
        errorCount: prev.errorCount + 1
      }))

      addAlert({
        type: 'error',
        message: `JavaScript error: ${event.message}`,
        metric: 'errorCount',
        value: metrics.errorCount + 1,
        threshold: 0
      })
    }

    window.addEventListener('error', handleError)
    return () => window.removeEventListener('error', handleError)
  }, [addAlert, metrics.errorCount])

  const getPerformanceScore = useCallback(() => {
    let score = 100
    
    // Deduct points for performance issues
    if (metrics.loadTime > thresholds.loadTime) {
      score -= 20
    }
    if (metrics.memoryUsage > thresholds.memoryUsage) {
      score -= 25
    }
    if (Object.values(metrics.chartRenderTimes).some(time => time > thresholds.chartRenderTime)) {
      score -= 15
    }
    if (metrics.scrollPerformance > thresholds.scrollPerformance) {
      score -= 10
    }
    if (metrics.errorCount > 0) {
      score -= metrics.errorCount * 5
    }

    return Math.max(0, score)
  }, [metrics])

  const exportMetrics = useCallback(() => {
    // Only export on the client side
    if (typeof window === 'undefined' || typeof navigator === 'undefined' || typeof document === 'undefined') {
      return
    }

    const report = {
      timestamp: new Date().toISOString(),
      metrics,
      alerts,
      performanceScore: getPerformanceScore(),
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    }

    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: 'application/json'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-report-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }, [metrics, alerts, getPerformanceScore])

  const clearAlerts = useCallback(() => {
    setAlerts([])
  }, [])

  return {
    metrics,
    alerts,
    measureChartRender,
    measureScrollPerformance,
    getPerformanceScore,
    exportMetrics,
    clearAlerts,
    thresholds
  }
}
