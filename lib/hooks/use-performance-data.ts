"use client"

import { useState, useEffect, useCallback } from 'react'

interface PerformanceData {
  overview: any
  trends: any[]
  platforms: any[]
  goals: any[]
  timestamp: number
}

interface ContentData {
  data: any[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  meta: {
    sortBy: string
    search: string
    timestamp: number
  }
}

interface UsePerformanceDataOptions {
  section?: string
  autoRefresh?: boolean
  refreshInterval?: number
}

interface UseContentDataOptions {
  page?: number
  limit?: number
  sortBy?: string
  search?: string
  autoRefresh?: boolean
}

// Custom hook for performance data
export function usePerformanceData(options: UsePerformanceDataOptions = {}) {
  const { section, autoRefresh = false, refreshInterval = 300000 } = options // 5 minutes default
  
  const [data, setData] = useState<PerformanceData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const url = section 
        ? `/api/dashboard/performance?section=${section}`
        : '/api/dashboard/performance'
      
      const response = await fetch(url, {
        headers: {
          'Cache-Control': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch data')
      }

      setData(result.data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
      console.error('Performance data fetch error:', err)
    } finally {
      setLoading(false)
    }
  }, [section])

  useEffect(() => {
    fetchData()

    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(fetchData, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [fetchData, autoRefresh, refreshInterval])

  return {
    data,
    loading,
    error,
    refetch: fetchData
  }
}

// Custom hook for content data with pagination
export function useContentData(options: UseContentDataOptions = {}) {
  const { 
    page = 1, 
    limit = 50, 
    sortBy = 'score', 
    search = '',
    autoRefresh = false 
  } = options

  const [data, setData] = useState<ContentData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sortBy,
        ...(search && { search })
      })

      const response = await fetch(`/api/dashboard/performance/content?${params}`, {
        headers: {
          'Cache-Control': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch content data')
      }

      setData(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
      console.error('Content data fetch error:', err)
    } finally {
      setLoading(false)
    }
  }, [page, limit, sortBy, search])

  useEffect(() => {
    fetchData()

    if (autoRefresh) {
      const interval = setInterval(fetchData, 600000) // 10 minutes
      return () => clearInterval(interval)
    }
  }, [fetchData, autoRefresh])

  return {
    data,
    loading,
    error,
    refetch: fetchData
  }
}

// Performance monitoring hook
export function usePerformanceMonitoring() {
  const [metrics, setMetrics] = useState({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    isFirefox: false
  })

  useEffect(() => {
    const startTime = performance.now()
    const isFirefox = navigator.userAgent.includes('Firefox')

    // Measure initial load time
    const measureLoadTime = () => {
      const loadTime = performance.now() - startTime
      setMetrics(prev => ({ ...prev, loadTime, isFirefox }))
    }

    // Measure memory usage (if available)
    const measureMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        setMetrics(prev => ({ 
          ...prev, 
          memoryUsage: memory.usedJSHeapSize / 1024 / 1024 // MB
        }))
      }
    }

    measureLoadTime()
    measureMemory()

    // Set up periodic memory monitoring
    const interval = setInterval(measureMemory, 30000) // Every 30 seconds

    return () => clearInterval(interval)
  }, [])

  return metrics
}
