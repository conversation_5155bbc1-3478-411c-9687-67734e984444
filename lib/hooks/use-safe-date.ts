"use client"

import { useState, useEffect } from 'react'

/**
 * Hook for handling dates safely during SSR/hydration
 * Prevents hydration mismatches by ensuring consistent server/client rendering
 */
export function useSafeDate(initialDate?: Date) {
  const [date, setDate] = useState<Date | undefined>(initialDate)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    // Only set current date on client side if no initial date provided
    if (!initialDate) {
      setDate(new Date())
    }
  }, [initialDate])

  return { 
    date, 
    isClient, 
    setDate,
    // Helper to safely format dates
    safeFormat: (formatter: (date: Date) => string, fallback = "Loading...") => {
      return isClient && date ? formatter(date) : fallback
    }
  }
}

/**
 * Hook for handling date ranges safely during SSR/hydration
 */
export function useSafeDateRange(initialFrom?: Date, initialTo?: Date) {
  const [dateRange, setDateRange] = useState<{from?: Date, to?: Date}>({
    from: initialFrom,
    to: initialTo
  })
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    // Set current date as 'to' if not provided
    if (!initialTo) {
      setDateRange(prev => ({
        ...prev,
        to: new Date()
      }))
    }
  }, [initialTo])

  return {
    dateRange,
    isClient,
    setDateRange,
    // Helper to safely format date ranges
    safeFormatRange: (
      formatter: (from?: Date, to?: Date) => string, 
      fallback = "Select date range"
    ) => {
      return isClient ? formatter(dateRange.from, dateRange.to) : fallback
    }
  }
}

/**
 * Hook for any client-only operations to prevent hydration mismatches
 */
export function useClientOnly<T>(
  clientValue: () => T,
  serverValue?: T
) {
  const [value, setValue] = useState<T | undefined>(serverValue)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    setValue(clientValue())
  }, [])

  return { value, isClient }
}
