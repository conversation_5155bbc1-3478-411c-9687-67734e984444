"use client"

// Firefox-specific performance optimizations
export class FirefoxOptimizer {
  private static instance: FirefoxOptimizer
  private isFirefox: boolean
  private optimizationsApplied: boolean = false

  constructor() {
    this.isFirefox = typeof window !== 'undefined' && navigator.userAgent.includes('Firefox')
  }

  static getInstance(): FirefoxOptimizer {
    if (!FirefoxOptimizer.instance) {
      FirefoxOptimizer.instance = new FirefoxOptimizer()
    }
    return FirefoxOptimizer.instance
  }

  // Apply Firefox-specific optimizations
  applyOptimizations(): void {
    if (!this.isFirefox || this.optimizationsApplied) return

    this.optimizeDOM()
    this.optimizeAnimations()
    this.optimizeMemory()
    this.optimizeRendering()
    
    this.optimizationsApplied = true
    console.log('Firefox optimizations applied')
  }

  // Optimize DOM performance
  private optimizeDOM(): void {
    // Add CSS containment for better performance
    const style = document.createElement('style')
    style.textContent = `
      /* Firefox-specific optimizations */
      .chart-container {
        contain: layout style paint;
        will-change: transform;
        transform: translateZ(0);
      }
      
      .dashboard-card {
        contain: layout style;
      }
      
      .virtual-list {
        contain: strict;
        overflow: hidden;
      }
      
      /* Reduce repaints in Firefox */
      .performance-metric {
        backface-visibility: hidden;
        perspective: 1000px;
      }
      
      /* Optimize scrolling */
      .scrollable {
        scroll-behavior: auto;
        -webkit-overflow-scrolling: touch;
      }
      
      /* Reduce layout thrashing */
      .grid-container {
        contain: layout;
      }
    `
    document.head.appendChild(style)
  }

  // Optimize animations for Firefox
  private optimizeAnimations(): void {
    // Disable heavy animations in Firefox
    const style = document.createElement('style')
    style.textContent = `
      @media screen and (-moz-appearance: none) {
        * {
          animation-duration: 0.1s !important;
          transition-duration: 0.1s !important;
        }
        
        .animate-pulse {
          animation: none !important;
        }
        
        .animate-spin {
          animation-duration: 0.5s !important;
        }
      }
    `
    document.head.appendChild(style)
  }

  // Optimize memory usage
  private optimizeMemory(): void {
    // Set up memory monitoring
    if ('memory' in performance) {
      const checkMemory = () => {
        const memory = (performance as any).memory
        const usedMB = memory.usedJSHeapSize / 1024 / 1024
        
        // If memory usage is high, trigger cleanup
        if (usedMB > 100) {
          this.triggerCleanup()
        }
      }

      // Check memory every 30 seconds
      setInterval(checkMemory, 30000)
    }
  }

  // Optimize rendering performance
  private optimizeRendering(): void {
    // Use requestIdleCallback for non-critical updates
    if ('requestIdleCallback' in window) {
      const optimizeRender = (callback: () => void) => {
        requestIdleCallback(callback, { timeout: 1000 })
      }

      // Expose globally for components to use
      ;(window as any).optimizeRender = optimizeRender
    }

    // Batch DOM updates
    let updateQueue: (() => void)[] = []
    let isUpdating = false

    const batchUpdate = (callback: () => void) => {
      updateQueue.push(callback)
      
      if (!isUpdating) {
        isUpdating = true
        requestAnimationFrame(() => {
          updateQueue.forEach(cb => cb())
          updateQueue = []
          isUpdating = false
        })
      }
    }

    ;(window as any).batchUpdate = batchUpdate
  }

  // Trigger memory cleanup
  private triggerCleanup(): void {
    // Force garbage collection if available
    if ('gc' in window) {
      ;(window as any).gc()
    }

    // Clear unused event listeners
    this.cleanupEventListeners()

    // Clear cached data
    this.clearCaches()
  }

  // Clean up event listeners
  private cleanupEventListeners(): void {
    // Remove unused event listeners from charts
    const chartElements = document.querySelectorAll('.chart-container')
    chartElements.forEach(element => {
      const clone = element.cloneNode(true)
      element.parentNode?.replaceChild(clone, element)
    })
  }

  // Clear browser caches
  private clearCaches(): void {
    // Clear service worker caches if needed
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          if (name.includes('dashboard-cache')) {
            caches.delete(name)
          }
        })
      })
    }
  }

  // Check if optimizations are needed
  shouldOptimize(): boolean {
    return this.isFirefox && !this.optimizationsApplied
  }

  // Get Firefox-specific configuration
  getFirefoxConfig() {
    return {
      // Reduce chart animation duration
      animationDuration: 200,
      
      // Use smaller batch sizes for virtual scrolling
      virtualScrollBatchSize: 20,
      
      // Reduce concurrent chart renders
      maxConcurrentCharts: 2,
      
      // Use simpler chart types
      preferSimpleCharts: true,
      
      // Disable heavy effects
      disableBlur: true,
      disableShadows: true,
      
      // Memory management
      enableMemoryMonitoring: true,
      memoryThreshold: 100, // MB
      
      // Rendering optimizations
      useRequestIdleCallback: true,
      batchDOMUpdates: true
    }
  }
}

// Hook for using Firefox optimizations
export function useFirefoxOptimizations() {
  const optimizer = FirefoxOptimizer.getInstance()
  
  // Apply optimizations on mount
  if (typeof window !== 'undefined') {
    optimizer.applyOptimizations()
  }

  return {
    isFirefox: optimizer.shouldOptimize(),
    config: optimizer.getFirefoxConfig()
  }
}

// Utility function to check if running in Firefox
export function isFirefox(): boolean {
  return typeof window !== 'undefined' && navigator.userAgent.includes('Firefox')
}

// Optimized chart configuration for Firefox
export function getFirefoxChartConfig() {
  if (!isFirefox()) return {}

  return {
    animation: {
      duration: 200,
      easing: 'linear'
    },
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false // Reduce DOM complexity
      },
      tooltip: {
        enabled: false // Disable for better performance
      }
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false // Reduce rendering complexity
        }
      },
      y: {
        display: true,
        grid: {
          display: false
        }
      }
    }
  }
}
