"use client"

import React, { memo, lazy, Suspense, useRef, useEffect, useState } from 'react'

// Lightweight chart implementation using Canvas API
interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string
    borderColor?: string
    fill?: boolean
  }[]
}

interface ChartProps {
  type: 'line' | 'bar' | 'area'
  data: ChartData
  width?: number
  height?: number
  className?: string
}

// Optimized Canvas Chart Component (replaces Recharts)
const CanvasChart = memo(function CanvasChart({ 
  type, 
  data, 
  width = 400, 
  height = 300,
  className = ""
}: ChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isVisible, setIsVisible] = useState(false)

  // Intersection Observer for lazy rendering
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )

    observer.observe(canvas)
    return () => observer.disconnect()
  }, [])

  // Chart rendering logic
  useEffect(() => {
    if (!isVisible) return
    
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size for high DPI displays
    const dpr = window.devicePixelRatio || 1
    canvas.width = width * dpr
    canvas.height = height * dpr
    canvas.style.width = `${width}px`
    canvas.style.height = `${height}px`
    ctx.scale(dpr, dpr)

    // Clear canvas
    ctx.clearRect(0, 0, width, height)

    // Chart rendering based on type
    if (type === 'line' || type === 'area') {
      renderLineChart(ctx, data, width, height, type === 'area')
    } else if (type === 'bar') {
      renderBarChart(ctx, data, width, height)
    }
  }, [isVisible, data, type, width, height])

  return (
    <div className={`relative ${className}`}>
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ maxWidth: width, maxHeight: height }}
      />
      {!isVisible && (
        <div 
          className="absolute inset-0 bg-muted animate-pulse rounded flex items-center justify-center"
          style={{ width, height }}
        >
          <span className="text-muted-foreground text-sm">Loading chart...</span>
        </div>
      )}
    </div>
  )
})

// Line/Area chart renderer
function renderLineChart(
  ctx: CanvasRenderingContext2D, 
  data: ChartData, 
  width: number, 
  height: number,
  filled: boolean = false
) {
  const padding = 40
  const chartWidth = width - padding * 2
  const chartHeight = height - padding * 2

  if (!data.datasets.length || !data.labels.length) return

  const dataset = data.datasets[0]
  const maxValue = Math.max(...dataset.data)
  const minValue = Math.min(...dataset.data)
  const valueRange = maxValue - minValue || 1

  // Draw axes
  ctx.strokeStyle = '#e5e7eb'
  ctx.lineWidth = 1
  ctx.beginPath()
  ctx.moveTo(padding, padding)
  ctx.lineTo(padding, height - padding)
  ctx.lineTo(width - padding, height - padding)
  ctx.stroke()

  // Draw data line
  ctx.strokeStyle = dataset.borderColor || '#3b82f6'
  ctx.lineWidth = 2
  ctx.beginPath()

  const points: [number, number][] = []

  dataset.data.forEach((value, index) => {
    const x = padding + (index / (data.labels.length - 1)) * chartWidth
    const y = height - padding - ((value - minValue) / valueRange) * chartHeight
    points.push([x, y])

    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })

  ctx.stroke()

  // Fill area if needed
  if (filled) {
    ctx.fillStyle = dataset.backgroundColor || 'rgba(59, 130, 246, 0.1)'
    ctx.beginPath()
    ctx.moveTo(points[0][0], height - padding)
    points.forEach(([x, y]) => ctx.lineTo(x, y))
    ctx.lineTo(points[points.length - 1][0], height - padding)
    ctx.closePath()
    ctx.fill()
  }

  // Draw data points
  ctx.fillStyle = dataset.borderColor || '#3b82f6'
  points.forEach(([x, y]) => {
    ctx.beginPath()
    ctx.arc(x, y, 3, 0, Math.PI * 2)
    ctx.fill()
  })
}

// Bar chart renderer
function renderBarChart(
  ctx: CanvasRenderingContext2D, 
  data: ChartData, 
  width: number, 
  height: number
) {
  const padding = 40
  const chartWidth = width - padding * 2
  const chartHeight = height - padding * 2

  if (!data.datasets.length || !data.labels.length) return

  const dataset = data.datasets[0]
  const maxValue = Math.max(...dataset.data)
  const barWidth = chartWidth / data.labels.length * 0.8
  const barSpacing = chartWidth / data.labels.length * 0.2

  // Draw axes
  ctx.strokeStyle = '#e5e7eb'
  ctx.lineWidth = 1
  ctx.beginPath()
  ctx.moveTo(padding, padding)
  ctx.lineTo(padding, height - padding)
  ctx.lineTo(width - padding, height - padding)
  ctx.stroke()

  // Draw bars
  ctx.fillStyle = dataset.backgroundColor || '#3b82f6'
  dataset.data.forEach((value, index) => {
    const barHeight = (value / maxValue) * chartHeight
    const x = padding + index * (barWidth + barSpacing) + barSpacing / 2
    const y = height - padding - barHeight

    ctx.fillRect(x, y, barWidth, barHeight)
  })
}

// Chart skeleton component
export const OptimizedChartSkeleton = memo(function OptimizedChartSkeleton({ 
  height = 300,
  className = ""
}: { 
  height?: number
  className?: string 
}) {
  return (
    <div 
      className={`w-full bg-muted animate-pulse rounded flex items-center justify-center ${className}`}
      style={{ height }}
    >
      <div className="text-muted-foreground text-sm">Loading chart...</div>
    </div>
  )
})

// Chart provider for optimized charts
interface OptimizedChartProviderProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  height?: number
}

export function OptimizedChartProvider({ 
  children, 
  fallback, 
  height = 300
}: OptimizedChartProviderProps) {
  return (
    <Suspense fallback={fallback || <OptimizedChartSkeleton height={height} />}>
      {children}
    </Suspense>
  )
}

// Export optimized chart component
export { CanvasChart as OptimizedChart }

// Utility function to convert Recharts data format
export function convertToOptimizedFormat(rechartsData: any[]): ChartData {
  if (!rechartsData.length) {
    return { labels: [], datasets: [] }
  }

  const labels = rechartsData.map(item => item.date || item.name || '')
  const dataKeys = Object.keys(rechartsData[0]).filter(key => 
    key !== 'date' && key !== 'name' && typeof rechartsData[0][key] === 'number'
  )

  const datasets = dataKeys.map((key, index) => ({
    label: key,
    data: rechartsData.map(item => item[key] || 0),
    borderColor: `hsl(${index * 60}, 70%, 50%)`,
    backgroundColor: `hsla(${index * 60}, 70%, 50%, 0.1)`,
    fill: false
  }))

  return { labels, datasets }
}
